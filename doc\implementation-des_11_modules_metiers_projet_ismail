# Documentation Complète d'Implémentation des 11 Modules Métiers du Projet ISMAIL

## 1. ISMAIL Shop (E-commerce)

### 1.1 Architecture Technique

#### 1.1.1 Schéma d'Architecture Global

```
+---------------------+      +---------------------+      +---------------------+
| Frontend (React)    |<---->| API Gateway         |<---->| ISMAIL Core         |
| (PWA)               |      | (Netlify)           |      | (Services transverses) |
+---------------------+      +---------------------+      +---------------------+
       ▲                          ▲                          ▲
       |                          |                          |
       |                          |                          |
       ▼                          ▼                          ▼
+----------------------+      +---------------------+      +---------------------+
| Shop Module          |<---->| Event Bus           |<---->| Data Pipeline       |
| (Microservice)       |      | (Kafka)             |      | (Stream Processing) |
+----------------------+      +---------------------+      +---------------------+
       ▲                          ▲                          ▲
       |                          |                          |
       |                          |                          |
       ▼                          ▼                          ▼
+----------------------+      +---------------------+      +---------------------+
| Base de Données      |      | Services Externes   |      | Base de Données     |
| (Neon PostgreSQL)    |      | (Wave, Payment)     |      | (PostgreSQL Neon)   |
+----------------------+      +---------------------+      +---------------------+
```

#### 1.1.2 Structure de Dossier du Module

```
ismail-shop/
├── src/
│   ├── components/
│   │   ├── product/
│   │   │   ├── ProductCard.tsx
│   │   │   ├── ProductDetails.tsx
│   │   │   ├── ProductGallery.tsx
│   │   │   └── ProductRating.tsx
│   │   ├── cart/
│   │   │   ├── CartSummary.tsx
│   │   │   ├── CartItem.tsx
│   │   │   └── CheckoutForm.tsx
│   │   ├── category/
│   │   │   ├── CategoryTree.tsx
│   │   │   ├── CategoryFilter.tsx
│   │   │   └── CategoryBanner.tsx
│   │   ├── search/
│   │   │   ├── SearchBar.tsx
│   │   │   ├── SearchResult.tsx
│   │   │   └── SearchFilters.tsx
│   │   ├── order/
│   │   │   ├── OrderConfirmation.tsx
│   │   │   ├── OrderTracking.tsx
│   │   │   └── OrderHistory.tsx
│   │   ├── common/
│   │   │   ├── Button.tsx
│   │   │   ├── Input.tsx
│   │   │   ├── Rating.tsx
│   │   │   └── Loader.tsx
│   ├── services/
│   │   ├── api/
│   │   │   ├── shopApi.ts
│   │   │   ├── paymentApi.ts
│   │   │   └── deliveryApi.ts
│   │   ├── cart/
│   │   │   ├── cartService.ts
│   │   │   └── cartReducer.ts
│   │   ├── product/
│   │   │   ├── productService.ts
│   │   │   └── productFilterService.ts
│   │   └── order/
│   │       ├── orderService.ts
│   │       └── orderValidation.ts
│   ├── hooks/
│   │   ├── useCart.ts
│   │   ├── useProduct.ts
│   │   └── useOrder.ts
│   ├── pages/
│   │   ├── Home.tsx
│   │   ├── ProductList.tsx
│   │   ├── ProductDetail.tsx
│   │   ├── Cart.tsx
│   │   ├── Checkout.tsx
│   │   ├── OrderHistory.tsx
│   │   ├── OrderDetail.tsx
│   │   └── Search.tsx
│   ├── contexts/
│   │   ├── CartContext.tsx
│   │   ├── AuthContext.tsx
│   │   └── ProductContext.tsx
│   ├── utils/
│   │   ├── currency.ts
│   │   ├── validation.ts
│   │   ├── geolocation.ts
│   │   └── image.ts
│   ├── styles/
│   │   ├── global.scss
│   │   ├── theme.scss
│   │   └── components/
│   │       ├── button.scss
│   │       ├── card.scss
│   │       └── form.scss
│   └── app.tsx
├── netlify.toml
├── package.json
└── tsconfig.json
```

### 1.2 Structure de Données

#### 1.2.1 Schéma de Base de Données

```
products
- id(UUID) PRIMARY KEY
- user_id(UUID) REFERENCES users(id) NOT NULL
- name(varchar(255)) NOT NULL
- description(text) NOT NULL
- category_id(UUID) REFERENCES product_categories(id) NOT NULL
- subcategory_id(UUID) REFERENCES product_subcategories(id)
- price(numeric(10,2)) NOT NULL
- sale_price(numeric(10,2))
- stock(int) DEFAULT 0
- sku(varchar(50)) UNIQUE
- barcode(varchar(50)) UNIQUE
- images(jsonb) NOT NULL
- specifications(jsonb)
- tags(jsonb)
- status(varchar(20)) DEFAULT 'active' CHECK(status IN ('active', 'inactive', 'out_of_stock', 'discontinued'))
- created_at(timestamptz) DEFAULT CURRENT_TIMESTAMP
- updated_at(timestamptz) DEFAULT CURRENT_TIMESTAMP

product_categories
- id(UUID) PRIMARY KEY
- name(varchar(100)) NOT NULL
- description(text)
- parent_id(UUID) REFERENCES product_categories(id)
- created_at(timestamptz) DEFAULT CURRENT_TIMESTAMP
- updated_at(timestamptz) DEFAULT CURRENT_TIMESTAMP

product_subcategories
- id(UUID) PRIMARY KEY
- name(varchar(100)) NOT NULL
- category_id(UUID) REFERENCES product_categories(id) NOT NULL
- created_at(timestamptz) DEFAULT CURRENT_TIMESTAMP
- updated_at(timestamptz) DEFAULT CURRENT_TIMESTAMP

orders
- id(UUID) PRIMARY KEY
- user_id(UUID) REFERENCES users(id) NOT NULL
- total_amount(numeric(10,2)) NOT NULL
- shipping_amount(numeric(10,2)) NOT NULL
- tax_amount(numeric(10,2)) NOT NULL
- discount_amount(numeric(10,2)) NOT NULL
- status(varchar(20)) DEFAULT 'pending' CHECK(status IN ('pending', 'processing', 'shipped', 'delivered', 'cancelled', 'returned'))
- payment_method(varchar(50)) NOT NULL
- payment_status(varchar(20)) DEFAULT 'pending' CHECK(payment_status IN ('pending', 'completed', 'failed', 'refunded'))
- delivery_address(jsonb) NOT NULL
- delivery_method(varchar(50)) NOT NULL
- delivery_tracking_id(varchar(100))
- created_at(timestamptz) DEFAULT CURRENT_TIMESTAMP
- updated_at(timestamptz) DEFAULT CURRENT_TIMESTAMP

order_items
- id(UUID) PRIMARY KEY
- order_id(UUID) REFERENCES orders(id) NOT NULL
- product_id(UUID) REFERENCES products(id) NOT NULL
- quantity(int) NOT NULL
- price_per_unit(numeric(10,2)) NOT NULL
- total_price(numeric(10,2)) NOT NULL
- created_at(timestamptz) DEFAULT CURRENT_TIMESTAMP
- updated_at(timestamptz) DEFAULT CURRENT_TIMESTAMP

reviews
- id(UUID) PRIMARY KEY
- product_id(UUID) REFERENCES products(id) NOT NULL
- user_id(UUID) REFERENCES users(id) NOT NULL
- rating(int) NOT NULL CHECK(rating BETWEEN 1 AND 5)
- comment(text)
- status(varchar(20)) DEFAULT 'pending' CHECK(status IN ('pending', 'approved', 'rejected'))
- created_at(timestamptz) DEFAULT CURRENT_TIMESTAMP
- updated_at(timestamptz) DEFAULT CURRENT_TIMESTAMP

promotions
- id(UUID) PRIMARY KEY
- name(varchar(100)) NOT NULL
- description(text)
- type(varchar(50)) NOT NULL CHECK(type IN ('percentage', 'fixed', 'buy_x_get_y'))
- value(numeric(10,2)) NOT NULL
- start_date(timestamptz) NOT NULL
- end_date(timestamptz) NOT NULL
- min_order_amount(numeric(10,2))
- max_discount_amount(numeric(10,2))
- applicable_products(jsonb)
- applicable_categories(jsonb)
- status(varchar(20)) DEFAULT 'active' CHECK(status IN ('active', 'inactive'))
- created_at(timestamptz) DEFAULT CURRENT_TIMESTAMP
- updated_at(timestamptz) DEFAULT CURRENT_TIMESTAMP

coupons
- id(UUID) PRIMARY KEY
- code(varchar(50)) UNIQUE NOT NULL
- promotion_id(UUID) REFERENCES promotions(id) NOT NULL
- usage_limit(int)
- used_count(int) DEFAULT 0
- expiry_date(timestamptz) NOT NULL
- status(varchar(20)) DEFAULT 'active' CHECK(status IN ('active', 'inactive'))
- created_at(timestamptz) DEFAULT CURRENT_TIMESTAMP
- updated_at(timestamptz) DEFAULT CURRENT_TIMESTAMP

shipping_methods
- id(UUID) PRIMARY KEY
- name(varchar(100)) NOT NULL
- description(text)
- base_price(numeric(10,2)) NOT NULL
- price_per_kg(numeric(10,2))
- delivery_time_days(int) NOT NULL
- applicable_regions(jsonb) NOT NULL
- status(varchar(20)) DEFAULT 'active' CHECK(status IN ('active', 'inactive'))
- created_at(timestamptz) DEFAULT CURRENT_TIMESTAMP
- updated_at(timestamptz) DEFAULT CURRENT_TIMESTAMP
```

#### 1.2.2 Exemple de Structure de Données pour un Produit

```json
{
  "id": "a1b2c3d4-5678-90ab-cdef-1234567890ab",
  "user_id": "e1f2a3b4-5678-90ab-cdef-1234567890cd",
  "name": "Smartphone Xiaomi Redmi Note 10 Pro",
  "description": "Smartphone haut de gamme avec écran AMOLED 120Hz, caméra quadri-capteur 108MP et batterie 5000mAh",
  "category_id": "f5a6b7c8-d9e0-1234-5678-90abcdef1234",
  "subcategory_id": "g5h6i7j8-k9l0-1234-5678-90abcdef5678",
  "price": 250000.00,
  "sale_price": 225000.00,
  "stock": 50,
  "sku": "XIAOMI-RN10P-001",
  "barcode": "8801234567890",
  "images": [
    {
      "url": "https://example.com/images/xiaomi-rn10p-1.jpg",
      "alt": "Smartphone Xiaomi Redmi Note 10 Pro front view",
      "is_primary": true
    },
    {
      "url": "https://example.com/images/xiaomi-rn10p-2.jpg",
      "alt": "Smartphone Xiaomi Redmi Note 10 Pro back view"
    },
    {
      "url": "https://example.com/images/xiaomi-rn10p-3.jpg",
      "alt": "Smartphone Xiaomi Redmi Note 10 Pro side view"
    }
  ],
  "specifications": {
    "display": {
      "type": "AMOLED",
      "size": "6.67 inches",
      "resolution": "2400 x 1080 pixels",
      "refresh_rate": "120Hz"
    },
    "camera": {
      "rear": "108MP main + 8MP ultra-wide + 5MP macro + 2MP depth",
      "front": "16MP"
    },
    "battery": {
      "capacity": "5000mAh",
      "charging": "33W fast charging"
    },
    "processor": "Qualcomm Snapdragon 732G",
    "storage": "128GB"
  },
  "tags": ["smartphone", "xiaomi", "high-end", "camera", "fast-charging"],
  "status": "active",
  "created_at": "2023-01-15T12:30:00Z",
  "updated_at": "2023-01-15T12:30:00Z"
}
```

### 1.3 APIs Clés

#### 1.3.1 Gestion des Produits

```
POST /api/products
```

**Description**: Création d'un nouveau produit

**Requête**:
```json
{
  "name": "Smartphone Xiaomi Redmi Note 10 Pro",
  "description": "Smartphone haut de gamme avec écran AMOLED 120Hz, caméra quadri-capteur 108MP et batterie 5000mAh",
  "category_id": "f5a6b7c8-d9e0-1234-5678-90abcdef1234",
  "subcategory_id": "g5h6i7j8-k9l0-1234-5678-90abcdef5678",
  "price": 250000.00,
  "sale_price": 225000.00,
  "stock": 50,
  "sku": "XIAOMI-RN10P-001",
  "barcode": "8801234567890",
  "images": [
    {
      "url": "https://example.com/images/xiaomi-rn10p-1.jpg",
      "alt": "Smartphone Xiaomi Redmi Note 10 Pro front view",
      "is_primary": true
    },
    {
      "url": "https://example.com/images/xiaomi-rn10p-2.jpg",
      "alt": "Smartphone Xiaomi Redmi Note 10 Pro back view"
    },
    {
      "url": "https://example.com/images/xiaomi-rn10p-3.jpg",
      "alt": "Smartphone Xiaomi Redmi Note 10 Pro side view"
    }
  ],
  "specifications": {
    "display": {
      "type": "AMOLED",
      "size": "6.67 inches",
      "resolution": "2400 x 1080 pixels",
      "refresh_rate": "120Hz"
    },
    "camera": {
      "rear": "108MP main + 8MP ultra-wide + 5MP macro + 2MP depth",
      "front": "16MP"
    },
    "battery": {
      "capacity": "5000mAh",
      "charging": "33W fast charging"
    },
    "processor": "Qualcomm Snapdragon 732G",
    "storage": "128GB"
  },
  "tags": ["smartphone", "xiaomi", "high-end", "camera", "fast-charging"]
}
```

**Réponse**:
```json
{
  "id": "a1b2c3d4-5678-90ab-cdef-1234567890ab",
  "name": "Smartphone Xiaomi Redmi Note 10 Pro",
  "description": "Smartphone haut de gamme avec écran AMOLED 120Hz, caméra quadri-capteur 108MP et batterie 5000mAh",
  "category_id": "f5a6b7c8-d9e0-1234-5678-90abcdef1234",
  "subcategory_id": "g5h6i7j8-k9l0-1234-5678-90abcdef5678",
  "price": 250000.00,
  "sale_price": 225000.00,
  "stock": 50,
  "sku": "XIAOMI-RN10P-001",
  "barcode": "8801234567890",
  "images": [
    {
      "url": "https://example.com/images/xiaomi-rn10p-1.jpg",
      "alt": "Smartphone Xiaomi Redmi Note 10 Pro front view",
      "is_primary": true
    },
    {
      "url": "https://example.com/images/xiaomi-rn10p-2.jpg",
      "alt": "Smartphone Xiaomi Redmi Note 10 Pro back view"
    },
    {
      "url": "https://example.com/images/xiaomi-rn10p-3.jpg",
      "alt": "Smartphone Xiaomi Redmi Note 10 Pro side view"
    }
  ],
  "specifications": {
    "display": {
      "type": "AMOLED",
      "size": "6.67 inches",
      "resolution": "2400 x 1080 pixels",
      "refresh_rate": "120Hz"
    },
    "camera": {
      "rear": "108MP main + 8MP ultra-wide + 5MP macro + 2MP depth",
      "front": "16MP"
    },
    "battery": {
      "capacity": "5000mAh",
      "charging": "33W fast charging"
    },
    "processor": "Qualcomm Snapdragon 732G",
    "storage": "128GB"
  },
  "tags": ["smartphone", "xiaomi", "high-end", "camera", "fast-charging"],
  "status": "active",
  "created_at": "2023-01-15T12:30:00Z",
  "updated_at": "2023-01-15T12:30:00Z"
}
```

```
GET /api/products/{id}
```

**Description**: Récupération des détails d'un produit

**Réponse**:
```json
{
  "id": "a1b2c3d4-5678-90ab-cdef-1234567890ab",
  "name": "Smartphone Xiaomi Redmi Note 10 Pro",
  "description": "Smartphone haut de gamme avec écran AMOLED 120Hz, caméra quadri-capteur 108MP et batterie 5000mAh",
  "category_id": "f5a6b7c8-d9e0-1234-5678-90abcdef1234",
  "subcategory_id": "g5h6i7j8-k9l0-1234-5678-90abcdef5678",
  "price": 250000.00,
  "sale_price": 225000.00,
  "stock": 50,
  "sku": "XIAOMI-RN10P-001",
  "barcode": "8801234567890",
  "images": [
    {
      "url": "https://example.com/images/xiaomi-rn10p-1.jpg",
      "alt": "Smartphone Xiaomi Redmi Note 10 Pro front view",
      "is_primary": true
    },
    {
      "url": "https://example.com/images/xiaomi-rn10p-2.jpg",
      "alt": "Smartphone Xiaomi Redmi Note 10 Pro back view"
    },
    {
      "url": "https://example.com/images/xiaomi-rn10p-3.jpg",
      "alt": "Smartphone Xiaomi Redmi Note 10 Pro side view"
    }
  ],
  "specifications": {
    "display": {
      "type": "AMOLED",
      "size": "6.67 inches",
      "resolution": "2400 x 1080 pixels",
      "refresh_rate": "120Hz"
    },
    "camera": {
      "rear": "108MP main + 8MP ultra-wide + 5MP macro + 2MP depth",
      "front": "16MP"
    },
    "battery": {
      "capacity": "5000mAh",
      "charging": "33W fast charging"
    },
    "processor": "Qualcomm Snapdragon 732G",
    "storage": "128GB"
  },
  "tags": ["smartphone", "xiaomi", "high-end", "camera", "fast-charging"],
  "status": "active",
  "created_at": "2023-01-15T12:30:00Z",
  "updated_at": "2023-01-15T12:30:00Z"
}
```

```
GET /api/products
```

**Description**: Recherche de produits avec filtres

**Paramètres**:
- `category_id`: Filtrer par catégorie
- `subcategory_id`: Filtrer par sous-catégorie
- `min_price`: Prix minimum
- `max_price`: Prix maximum
- `sort_by`: Tri (price, popularity, latest)
- `search`: Recherche textuelle
- `page`: Page de résultats
- `limit`: Nombre de résultats par page

**Réponse**:
```json
{
  "total": 100,
  "page": 1,
  "limit": 20,
  "products": [
    {
      "id": "a1b2c3d4-5678-90ab-cdef-1234567890ab",
      "name": "Smartphone Xiaomi Redmi Note 10 Pro",
      "price": 250000.00,
      "sale_price": 225000.00,
      "images": [
        {
          "url": "https://example.com/images/xiaomi-rn10p-1.jpg",
          "alt": "Smartphone Xiaomi Redmi Note 10 Pro front view",
          "is_primary": true
        }
      ]
    },
    {
      "id": "b2c3d4e5-6789-0abc-def1-234567890123",
      "name": "Laptop Dell Inspiron 15",
      "price": 500000.00,
      "sale_price": 450000.00,
      "images": [
        {
          "url": "https://example.com/images/dell-inspiron-15.jpg",
          "alt": "Laptop Dell Inspiron 15",
          "is_primary": true
        }
      ]
    }
  ]
}
```

#### 1.3.2 Gestion des Commandes

```
POST /api/orders
```

**Description**: Création d'une nouvelle commande

**Requête**:
```json
{
  "user_id": "e1f2a3b4-5678-90ab-cdef-1234567890cd",
  "items": [
    {
      "product_id": "a1b2c3d4-5678-90ab-cdef-1234567890ab",
      "quantity": 2,
      "price_per_unit": 225000.00
    },
    {
      "product_id": "b2c3d4e5-6789-0abc-def1-234567890123",
      "quantity": 1,
      "price_per_unit": 450000.00
    }
  ],
  "shipping_address": {
    "street": "Avenue de la République",
    "city": "Dakar",
    "postal_code": "10000",
    "country": "SN",
    "phone": "+221771234567"
  },
  "delivery_method": "standard",
  "coupon_code": "WELCOME10"
}
```

**Réponse**:
```json
{
  "id": "c3d4e5f6-7890-1234-5678-90abcdef1234",
  "user_id": "e1f2a3b4-5678-90ab-cdef-1234567890cd",
  "total_amount": 900000.00,
  "shipping_amount": 5000.00,
  "tax_amount": 90000.00,
  "discount_amount": 95000.00,
  "status": "pending",
  "payment_method": "wave",
  "payment_status": "pending",
  "delivery_address": {
    "street": "Avenue de la République",
    "city": "Dakar",
    "postal_code": "10000",
    "country": "SN",
    "phone": "+221771234567"
  },
  "delivery_method": "standard",
  "delivery_tracking_id": "",
  "created_at": "2023-01-15T12:30:00Z",
  "updated_at": "2023-01-15T12:30:00Z"
}
```

```
POST /api/orders/{id}/pay
```

**Description**: Paiement d'une commande

**Requête**:
```json
{
  "payment_method": "wave",
  "amount": 900000.00,
  "wave_transaction_id": "WAVE-20230115-123456"
}
```

**Réponse**:
```json
{
  "id": "c3d4e5f6-7890-1234-5678-90abcdef1234",
  "user_id": "e1f2a3b4-5678-90ab-cdef-1234567890cd",
  "total_amount": 900000.00,
  "shipping_amount": 5000.00,
  "tax_amount": 90000.00,
  "discount_amount": 95000.00,
  "status": "processing",
  "payment_method": "wave",
  "payment_status": "completed",
  "delivery_address": {
    "street": "Avenue de la République",
    "city": "Dakar",
    "postal_code": "10000",
    "country": "SN",
    "phone": "+221771234567"
  },
  "delivery_method": "standard",
  "delivery_tracking_id": "WAVE-20230115-123456",
  "created_at": "2023-01-15T12:30:00Z",
  "updated_at": "2023-01-15T12:35:00Z"
}
```

### 1.4 Fonctionnalités Clés

#### 1.4.1 Interface de Catalogue Produits

**Implémentation détaillée**:

```tsx
// src/components/product/ProductCard.tsx
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { OptimizedImage } from '../common/OptimizedImage';
import { Button } from '../common/Button';
import { Rating } from '../common/Rating';
import { currencyFormat } from '../../utils/currency';

interface ProductCardProps {
  product: {
    id: string;
    name: string;
    price: number;
    sale_price?: number;
    images: { url: string; alt: string; is_primary: boolean }[];
    rating?: number;
    reviews_count?: number;
  };
  showActions?: boolean;
}

export const ProductCard: React.FC<ProductCardProps> = ({ product, showActions = true }) => {
  const navigate = useNavigate();
  
  const handleViewProduct = () => {
    navigate(`/products/${product.id}`);
  };

  return (
    <div className="product-card">
      <div className="product-card-image" onClick={handleViewProduct}>
        <OptimizedImage 
          src={product.images[0].url} 
          width={300} 
          height={300} 
          alt={product.images[0].alt} 
          quality={85}
        />
      </div>
      
      <div className="product-card-content">
        <h3 className="product-card-title" onClick={handleViewProduct}>
          {product.name}
        </h3>
        
        <div className="product-card-price">
          {product.sale_price ? (
            <>
              <span className="product-card-sale-price">
                {currencyFormat(product.sale_price)}
              </span>
              <span className="product-card-original-price">
                {currencyFormat(product.price)}
              </span>
            </>
          ) : (
            <span className="product-card-price">
              {currencyFormat(product.price)}
            </span>
          )}
        </div>
        
        {product.rating && (
          <div className="product-card-rating">
            <Rating value={product.rating} readOnly />
            <span className="product-card-reviews-count">
              ({product.reviews_count || 0})
            </span>
          </div>
        )}
        
        {showActions && (
          <div className="product-card-actions">
            <Button 
              variant="primary" 
              onClick={() => console.log('Add to cart')}
            >
              Ajouter au panier
            </Button>
            <Button 
              variant="secondary" 
              onClick={handleViewProduct}
            >
              Voir détails
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};
```

**Optimisation pour les terminaux mobiles**:

```tsx
// src/components/product/ProductGallery.tsx
import React, { useState, useRef, useEffect } from 'react';
import { OptimizedImage } from '../common/OptimizedImage';
import { Button } from '../common/Button';

interface ProductGalleryProps {
  images: { url: string; alt: string; is_primary: boolean }[];
}

export const ProductGallery: React.FC<ProductGalleryProps> = ({ images }) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isZoomed, setIsZoomed] = useState(false);
  const galleryRef = useRef<HTMLDivElement>(null);
  
  const handleImageClick = (index: number) => {
    setCurrentImageIndex(index);
  };

  const handleZoom = () => {
    setIsZoomed(true);
  };

  const handleZoomOut = () => {
    setIsZoomed(false);
  };

  useEffect(() => {
    if (isZoomed) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }
    
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isZoomed]);

  return (
    <div className="product-gallery" ref={galleryRef}>
      <div className="product-gallery-main">
        <OptimizedImage 
          src={images[currentImageIndex].url}
          width={800}
          height={800}
          alt={images[currentImageIndex].alt}
          quality={85}
          onClick={handleZoom}
          className={isZoomed ? 'zoomed' : ''}
        />
        
        {isZoomed && (
          <div className="product-gallery-zoom">
            <div className="product-gallery-zoom-content">
              <OptimizedImage 
                src={images[currentImageIndex].url}
                width={1200}
                height={1200}
                alt={images[currentImageIndex].alt}
                quality={85}
              />
              <Button 
                variant="secondary" 
                onClick={handleZoomOut}
                className="product-gallery-zoom-close"
              >
                Fermer
              </Button>
            </div>
          </div>
        )}
      </div>
      
      <div className="product-gallery-thumbnails">
        {images.map((image, index) => (
          <div 
            key={index}
            className={`product-gallery-thumbnail ${index === currentImageIndex ? 'active' : ''}`}
            onClick={() => handleImageClick(index)}
          >
            <OptimizedImage 
              src={image.url}
              width={80}
              height={80}
              alt={image.alt}
              quality={85}
            />
          </div>
        ))}
      </div>
    </div>
  );
};
```

**Optimisation pour les réseaux mobiles africains**:

```ts
// src/utils/image.ts
import { getStore } from "@netlify/blobs";
import { v4 as uuid } from "uuid";

export const optimizeImage = async (image: File, quality: number = 85): Promise<Blob> => {
  // Vérification de la taille de l'image
  if (image.size > 5 * 1024 * 1024) {
    // Si l'image est trop grande, la compresser
    return new Promise((resolve) => {
      const img = new Image();
      img.src = URL.createObjectURL(image);
      
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        // Calculer les nouvelles dimensions pour garder le ratio
        const maxWidth = 1200;
        const maxHeight = 1200;
        let width = img.width;
        let height = img.height;
        
        if (width > maxWidth) {
          height *= maxWidth / width;
          width = maxWidth;
        }
        if (height > maxHeight) {
          width *= maxHeight / height;
          height = maxHeight;
        }
        
        canvas.width = width;
        canvas.height = height;
        ctx?.drawImage(img, 0, 0, width, height);
        
        canvas.toBlob((blob) => {
          if (blob) {
            resolve(blob);
          } else {
            resolve(image);
          }
        }, 'image/webp', quality / 100);
      };
    });
  }
  
  // Si l'image est déjà petite, simplement convertir en WebP
  return new Promise((resolve) => {
    const img = new Image();
    img.src = URL.createObjectURL(image);
    
    img.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      canvas.width = img.width;
      canvas.height = img.height;
      ctx?.drawImage(img, 0, 0);
      
      canvas.toBlob((blob) => {
        if (blob) {
          resolve(blob);
        } else {
          resolve(image);
        }
      }, 'image/webp', quality / 100);
    };
  });
};

export const uploadImage = async (file: File, storeName: string = 'product-images'): Promise<string> => {
  const store = getStore(storeName);
  const key = uuid();
  
  try {
    // Optimiser l'image avant l'upload
    const optimizedImage = await optimizeImage(file);
    
    // Upload de l'image optimisée
    await store.set(key, optimizedImage, {
      metadata: {
        original_filename: file.name,
        content_type: file.type,
        size: file.size,
        uploaded_at: new Date().toISOString()
      }
    });
    
    // Retourner l'URL de l'image optimisée
    return `/.netlify/images?url=${key}&w=800&h=800&fm=webp&q=85`;
  } catch (error) {
    console.error('Erreur lors de l\'upload de l\'image:', error);
    throw error;
  }
};
```

#### 1.4.2 Système de Panier et Commande

**Implémentation détaillée du panier**:

```tsx
// src/hooks/useCart.ts
import { useState, useEffect, useContext, createContext } from 'react';
import { useAuth } from './useAuth';
import { cartService } from '../services/cart/cartService';
import { productApi } from '../services/api/shopApi';

interface CartItem {
  id: string;
  product_id: string;
  name: string;
  price: number;
  sale_price?: number;
  quantity: number;
  image_url: string;
  attributes?: Record<string, string>;
}

interface CartContextType {
  items: CartItem[];
  total: number;
  itemCount: number;
  addItem: (product: any, quantity?: number) => void;
  updateItem: (productId: string, quantity: number) => void;
  removeItem: (productId: string) => void;
  clearCart: () => void;
  loadCart: () => Promise<void>;
  checkout: () => Promise<void>;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export const CartProvider: React.FC = ({ children }) => {
  const [items, setItems] = useState<CartItem[]>([]);
  const [total, setTotal] = useState(0);
  const [itemCount, setItemCount] = useState(0);
  const { user } = useAuth();
  
  const loadCart = async () => {
    if (!user) return;
    
    try {
      const cartData = await cartService.getCart(user.id);
      setItems(cartData.items);
      calculateTotals(cartData.items);
    } catch (error) {
      console.error('Erreur lors du chargement du panier:', error);
    }
  };
  
  const addItem = (product: any, quantity = 1) => {
    if (!user) {
      // Gestion du panier local pour les utilisateurs non connectés
      const cart = JSON.parse(localStorage.getItem('cart') || '[]');
      const existingItem = cart.find((item: any) => item.product_id === product.id);
      
      if (existingItem) {
        existingItem.quantity += quantity;
      } else {
        cart.push({
          product_id: product.id,
          name: product.name,
          price: product.sale_price || product.price,
          sale_price: product.sale_price,
          quantity,
          image_url: product.images[0].url,
          attributes: product.attributes
        });
      }
      
      localStorage.setItem('cart', JSON.stringify(cart));
      calculateLocalTotals(cart);
      return;
    }
    
    // Gestion du panier pour les utilisateurs connectés
    cartService.addItem(user.id, product.id, quantity)
      .then(() => loadCart())
      .catch(error => console.error('Erreur lors de l\'ajout au panier:', error));
  };
  
  const updateItem = (productId: string, quantity: number) => {
    if (!user) {
      // Gestion du panier local pour les utilisateurs non connectés
      let cart = JSON.parse(localStorage.getItem('cart') || '[]');
      cart = cart.map((item: any) => 
        item.product_id === productId ? { ...item, quantity } : item
      );
      
      localStorage.setItem('cart', JSON.stringify(cart));
      calculateLocalTotals(cart);
      return;
    }
    
    // Gestion du panier pour les utilisateurs connectés
    cartService.updateItem(user.id, productId, quantity)
      .then(() => loadCart())
      .catch(error => console.error('Erreur lors de la mise à jour du panier:', error));
  };
  
  const removeItem = (productId: string) => {
    if (!user) {
      // Gestion du panier local pour les utilisateurs non connectés
      let cart = JSON.parse(localStorage.getItem('cart') || '[]');
      cart = cart.filter((item: any) => item.product_id !== productId);
      localStorage.setItem('cart', JSON.stringify(cart));
      calculateLocalTotals(cart);
      return;
    }
    
    // Gestion du panier pour les utilisateurs connectés
    cartService.removeItem(user.id, productId)
      .then(() => loadCart())
      .catch(error => console.error('Erreur lors de la suppression du panier:', error));
  };
  
  const clearCart = () => {
    if (!user) {
      localStorage.removeItem('cart');
      calculateLocalTotals([]);
      return;
    }
    
    cartService.clearCart(user.id)
      .then(() => loadCart())
      .catch(error => console.error('Erreur lors du vidage du panier:', error));
  };
  
  const calculateTotals = (items: CartItem[]) => {
    let total = 0;
    let count = 0;
    
    items.forEach(item => {
      const price = item.sale_price || item.price;
      total += price * item.quantity;
      count += item.quantity;
    });
    
    setTotal(total);
    setItemCount(count);
  };
  
  const calculateLocalTotals = (items: any[]) => {
    let total = 0;
    let count = 0;
    
    items.forEach(item => {
      total += item.price * item.quantity;
      count += item.quantity;
    });
    
    setTotal(total);
    setItemCount(count);
  };
  
  const checkout = async () => {
    if (!user) {
      throw new Error('Veuillez vous connecter pour passer une commande');
    }
    
    try {
      const orderData = await cartService.checkout(user.id);
      clearCart();
      return orderData;
    } catch (error) {
      console.error('Erreur lors du paiement:', error);
      throw error;
    }
  };
  
  useEffect(() => {
    if (user) {
      loadCart();
    } else {
      // Charger le panier local
      const cart = JSON.parse(localStorage.getItem('cart') || '[]');
      calculateLocalTotals(cart);
    }
  }, [user]);
  
  return (
    <CartContext.Provider value={{
      items,
      total,
      itemCount,
      addItem,
      updateItem,
      removeItem,
      clearCart,
      loadCart,
      checkout
    }}>
      {children}
    </CartContext.Provider>
  );
};

export const useCart = () => {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};
```

**Implémentation détaillée du formulaire de paiement**:

```tsx
// src/components/cart/CheckoutForm.tsx
import React, { useState, useEffect } from 'react';
import { useCart } from '../../hooks/useCart';
import { useAuth } from '../../hooks/useAuth';
import { Button } from '../common/Button';
import { Input } from '../common/Input';
import { useNavigate } from 'react-router-dom';
import { paymentApi } from '../../services/api/shopApi';
import { currencyFormat } from '../../utils/currency';
import { validateAddress } from '../../utils/validation';

interface CheckoutFormProps {
  onPaymentSuccess: (order: any) => void;
}

export const CheckoutForm: React.FC<CheckoutFormProps> = ({ onPaymentSuccess }) => {
  const { items, total, clearCart } = useCart();
  const { user } = useAuth();
  const navigate = useNavigate();
  
  const [shippingAddress, setShippingAddress] = useState({
    street: '',
    city: '',
    postal_code: '',
    country: 'SN',
    phone: ''
  });
  
  const [paymentMethod, setPaymentMethod] = useState('wave');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  useEffect(() => {
    if (user) {
      setShippingAddress({
        street: user.address?.street || '',
        city: user.address?.city || '',
        postal_code: user.address?.postal_code || '',
        country: user.address?.country || 'SN',
        phone: user.phone || ''
      });
    }
  }, [user]);
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation de l'adresse
    const addressValidation = validateAddress(shippingAddress);
    if (!addressValidation.valid) {
      setError(addressValidation.message);
      return;
    }
    
    // Validation du montant
    if (total <= 0) {
      setError('Le montant total doit être supérieur à zéro');
      return;
    }
    
    setLoading(true);
    setError('');
    
    try {
      // Créer la commande
      const orderData = await paymentApi.createOrder({
        items,
        shipping_address: shippingAddress,
        payment_method: paymentMethod,
        total_amount: total
      });
      
      // Rediriger vers la page de paiement
      if (paymentMethod === 'wave') {
        const paymentUrl = await paymentApi.getWavePaymentUrl(orderData.id, total);
        window.location.href = paymentUrl;
      } else {
        onPaymentSuccess(orderData);
        clearCart();
        navigate('/orders/success');
      }
    } catch (error) {
      console.error('Erreur lors du paiement:', error);
      setError('Une erreur est survenue lors du paiement. Veuillez réessayer.');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className="checkout-form">
      <h2>Adresse de livraison</h2>
      
      <div className="form-group">
        <Input
          label="Rue"
          name="street"
          value={shippingAddress.street}
          onChange={(e) => setShippingAddress({...shippingAddress, street: e.target.value})}
          required
        />
      </div>
      
      <div className="form-group">
        <Input
          label="Ville"
          name="city"
          value={shippingAddress.city}
          onChange={(e) => setShippingAddress({...shippingAddress, city: e.target.value})}
          required
        />
      </div>
      
      <div className="form-group">
        <Input
          label="Code postal"
          name="postal_code"
          value={shippingAddress.postal_code}
          onChange={(e) => setShippingAddress({...shippingAddress, postal_code: e.target.value})}
          required
        />
      </div>
      
      <div className="form-group">
        <Input
          label="Pays"
          name="country"
          value={shippingAddress.country}
          onChange={(e) => setShippingAddress({...shippingAddress, country: e.target.value})}
          required
        />
      </div>
      
      <div className="form-group">
        <Input
          label="Téléphone"
          name="phone"
          value={shippingAddress.phone}
          onChange={(e) => setShippingAddress({...shippingAddress, phone: e.target.value})}
          required
        />
      </div>
      
      <h2>Méthode de paiement</h2>
      
      <div className="payment-methods">
        <div className="payment-method">
          <input
            type="radio"
            id="wave"
            name="payment_method"
            value="wave"
            checked={paymentMethod === 'wave'}
            onChange={() => setPaymentMethod('wave')}
          />
          <label htmlFor="wave">Wave Mobile Money</label>
        </div>
        
        <div className="payment-method">
          <input
            type="radio"
            id="card"
            name="payment_method"
            value="card"
            checked={paymentMethod === 'card'}
            onChange={() => setPaymentMethod('card')}
          />
          <label htmlFor="card">Carte bancaire</label>
        </div>
        
        <div className="payment-method">
          <input
            type="radio"
            id="cash"
            name="payment_method"
            value="cash"
            checked={paymentMethod === 'cash'}
            onChange={() => setPaymentMethod('cash')}
          />
          <label htmlFor="cash">Paiement en espèces</label>
        </div>
      </div>
      
      {error && <div className="error-message">{error}</div>}
      
      <div className="checkout-summary">
        <h3>Résumé de la commande</h3>
        <div className="summary-item">
          <span>Sous-total:</span>
          <span>{currencyFormat(total)}</span>
        </div>
        <div className="summary-item">
          <span>Frais de livraison:</span>
          <span>5,000 F CFA</span>
        </div>
        <div className="summary-item total">
          <span>Total:</span>
          <span>{currencyFormat(total + 5000)}</span>
        </div>
      </div>
      
      <Button 
        type="submit" 
        variant="primary" 
        disabled={loading || total <= 0}
      >
        {loading ? 'Traitement en cours...' : `Payer ${currencyFormat(total + 5000)}`}
      </Button>
    </form>
  );
};
```

### 1.5 Spécificités pour le Marché Africain

#### 1.5.1 Adaptation aux réseaux mobiles africains

**Optimisation des images pour les réseaux 2G/3G**:

```ts
// src/utils/image.ts
export const optimizeForMobileNetwork = (image: File, networkType: '2g' | '3g' | '4g'): Blob => {
  let width = 800;
  let height = 800;
  let quality = 85;
  
  switch (networkType) {
    case '2g':
      width = 400;
      height = 400;
      quality = 65;
      break;
    case '3g':
      width = 600;
      height = 600;
      quality = 75;
      break;
    case '4g':
      width = 800;
      height = 800;
      quality = 85;
      break;
  }
  
  // Créer un canvas pour redimensionner et compresser l'image
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  
  const img = new Image();
  img.src = URL.createObjectURL(image);
  
  return new Promise((resolve) => {
    img.onload = () => {
      canvas.width = width;
      canvas.height = height;
      ctx?.drawImage(img, 0, 0, width, height);
      
      canvas.toBlob((blob) => {
        if (blob) {
          resolve(blob);
        } else {
          resolve(image);
        }
      }, 'image/webp', quality / 100);
    };
  });
};

// Dans le service de chargement des produits
export const loadProductImages = async (product: any, networkType: '2g' | '3g' | '4g') => {
  // Si l'utilisateur est sur un réseau 2G, optimiser les images
  if (networkType === '2g') {
    const optimizedImages = await Promise.all(
      product.images.map(async (image: any) => {
        const response = await fetch(image.url);
        const blob = await response.blob();
        const optimizedBlob = await optimizeForMobileNetwork(blob, networkType);
        return {
          ...image,
          url: URL.createObjectURL(optimizedBlob)
        };
      })
    );
    
    return {
      ...product,
      images: optimizedImages
    };
  }
  
  return product;
};

// Dans le composant ProductGallery
export const ProductGallery: React.FC<ProductGalleryProps> = ({ images }) => {
  // Détection du type de réseau
  const [networkType, setNetworkType] = useState<'2g' | '3g' | '4g'>('4g');
  
  useEffect(() => {
    // Utilisation de l'API Network Information pour détecter le type de réseau
    if ('connection' in navigator) {
      const connection = navigator.connection;
      if (connection) {
        if (connection.effectiveType === '2g') {
          setNetworkType('2g');
        } else if (connection.effectiveType === '3g') {
          setNetworkType('3g');
        } else {
          setNetworkType('4g');
        }
      }
    }
  }, []);
  
  // Utilisation de la détection de réseau pour charger les images optimisées
  const [optimizedImages, setOptimizedImages] = useState(images);
  
  useEffect(() => {
    if (networkType === '2g') {
      // Charger les images optimisées pour 2G
      const optimized = images.map(image => ({
        ...image,
        url: `${image.url}&w=400&h=400&fm=webp&q=65`
      }));
      setOptimizedImages(optimized);
    } else if (networkType === '3g') {
      // Charger les images optimisées pour 3G
      const optimized = images.map(image => ({
        ...image,
        url: `${image.url}&w=600&h=600&fm=webp&q=75`
      }));
      setOptimizedImages(optimized);
    } else {
      // Charger les images normales pour 4G
      setOptimizedImages(images);
    }
  }, [networkType, images]);
  
  // ... reste du composant
};
```

#### 1.5.2 Gestion des devises locales

**Implémentation détaillée du système de conversion de devises**:

```ts
// src/utils/currency.ts
interface Currency {
  code: string;
  symbol: string;
  name: string;
  rate: number;
}

const CURRENCIES: Record<string, Currency> = {
  'XOF': { code: 'XOF', symbol: 'FCFA', name: 'Franc CFA', rate: 1.0 },
  'XAF': { code: 'XAF', symbol: 'FCFA', name: 'Franc CFA', rate: 1.0 },
  'NGN': { code: 'NGN', symbol: '₦', name: 'Naira', rate: 650.0 },
  'KES': { code: 'KES', symbol: 'KSh', name: 'Shilling kenyan', rate: 120.0 },
  'UGX': { code: 'UGX', symbol: 'USh', name: 'Shilling ougandais', rate: 3700.0 },
  'ZAR': { code: 'ZAR', symbol: 'R', name: 'Rand', rate: 18.0 },
  'GHS': { code: 'GHS', symbol: 'GH¢', name: 'Cedi ghanéen', rate: 8.0 },
  'MZN': { code: 'MZN', symbol: 'MT', name: 'Metical', rate: 65.0 },
  'USD': { code: 'USD', symbol: '$', name: 'Dollar américain', rate: 600.0 },
  'EUR': { code: 'EUR', symbol: '€', name: 'Euro', rate: 650.0 }
};

export const getCurrencyRate = (code: string): number => {
  const currency = CURRENCIES[code];
  return currency ? currency.rate : 1.0;
};

export const convertCurrency = (amount: number, from: string, to: string): number => {
  const fromRate = getCurrencyRate(from);
  const toRate = getCurrencyRate(to);
  
  // Conversion via USD pour éviter les erreurs de calcul
  const usdAmount = amount / fromRate;
  return usdAmount * toRate;
};

export const currencyFormat = (amount: number, currency: string = 'XOF', locale: string = 'fr-CD'): string => {
  const currencyRate = getCurrencyRate(currency);
  const convertedAmount = amount * currencyRate;
  
  // Formatage selon la devise
  const options: Intl.NumberFormatOptions = {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  };
  
  return new Intl.NumberFormat(locale, options).format(convertedAmount);
};

export const currencySymbol = (currency: string = 'XOF'): string => {
  const currencyData = CURRENCIES[currency];
  return currencyData ? currencyData.symbol : 'FCFA';
};

// Dans le service de paiement
export const paymentApi = {
  createOrder: async (data: any) => {
    // Déterminer la devise en fonction du pays de l'utilisateur
    const userCountry = data.shipping_address.country;
    let currency = 'XOF';
    
    switch (userCountry) {
      case 'NG':
        currency = 'NGN';
        break;
      case 'KE':
        currency = 'KES';
        break;
      case 'UG':
        currency = 'UGX';
        break;
      case 'ZA':
        currency = 'ZAR';
        break;
      case 'GH':
        currency = 'GHS';
        break;
      case 'MZ':
        currency = 'MZN';
        break;
      default:
        currency = 'XOF';
    }
    
    // Convertir le montant total en devise locale
    const convertedTotal = convertCurrency(data.total_amount, 'XOF', currency);
    
    const response = await fetch('/api/orders', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Currency': currency
      },
      body: JSON.stringify({
        ...data,
        total_amount: convertedTotal,
        currency
      })
    });
    
    return await response.json();
  },
  
  getWavePaymentUrl: async (orderId: string, amount: number) => {
    const response = await fetch(`/api/payments/wave?order_id=${orderId}&amount=${amount}`);
    return await response.json().then(data => data.url);
  }
};
```

### 1.6 Intégration avec les Autres Modules

#### 1.6.1 Intégration avec ISMAIL Livraisons

**Implémentation détaillée du système de suivi des livraisons**:

```ts
// src/services/api/shopApi.ts
import { fetch } from 'whatwg-fetch';

export const shopApi = {
  // ... autres méthodes
  
  trackDelivery: async (trackingId: string) => {
    const response = await fetch(`/api/deliveries/${trackingId}`);
    return await response.json();
  },
  
  updateDeliveryStatus: async (trackingId: string, status: string, location?: any) => {
    const response = await fetch(`/api/deliveries/${trackingId}/status`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        status,
        location
      })
    });
    
    return await response.json();
  }
};

// Dans le composant OrderTracking
import { shopApi } from '../../services/api/shopApi';
import { deliveryApi } from '../../services/api/deliveryApi';

export const OrderTracking: React.FC = () => {
  const [tracking, setTracking] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  
  useEffect(() => {
    const trackingId = new URLSearchParams(window.location.search).get('id');
    
    if (!trackingId) {
      setError('ID de suivi non fourni');
      setLoading(false);
      return;
    }
    
    const fetchTracking = async () => {
      try {
        setLoading(true);
        const trackingData = await shopApi.trackDelivery(trackingId);
        setTracking(trackingData);
        setError('');
      } catch (error) {
        console.error('Erreur lors du suivi de la livraison:', error);
        setError('Impossible de récupérer les informations de suivi');
      } finally {
        setLoading(false);
      }
    };
    
    fetchTracking();
  }, []);
  
  // Mise à jour automatique des statuts de livraison
  useEffect(() => {
    if (!tracking) return;
    
    const interval = setInterval(async () => {
      try {
        const updatedTracking = await shopApi.trackDelivery(tracking.id);
        setTracking(updatedTracking);
      } catch (error) {
        console.error('Erreur lors de la mise à jour du suivi:', error);
      }
    }, 30000); // Mise à jour toutes les 30 secondes
    
    return () => clearInterval(interval);
  }, [tracking]);
  
  // Affichage des étapes de livraison
  const getDeliveryStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return 'pending';
      case 'assigned':
        return 'assigned';
      case 'in_transit':
        return 'in_transit';
      case 'delivered':
        return 'delivered';
      case 'canceled':
        return 'canceled';
      default:
        return 'pending';
    }
  };
  
  return (
    <div className="order-tracking">
      <h2>Suivi de votre livraison</h2>
      
      {loading ? (
        <Loader />
      ) : error ? (
        <div className="error-message">{error}</div>
      ) : (
        <>
          <div className="tracking-steps">
            {[
              { status: 'pending', label: 'Commande passée' },
              { status: 'assigned', label: 'Livreur assigné' },
              { status: 'in_transit', label: 'En route' },
              { status: 'delivered', label: 'Livré' },
              { status: 'canceled', label: 'Annulé' }
            ].map((step, index) => (
              <div 
                key={index}
                className={`tracking-step ${tracking.status === step.status ? 'active' : ''}`}
              >
                <div className={`status-icon ${getDeliveryStatusIcon(step.status)}`}></div>
                <div className="step-label">{step.label}</div>
              </div>
            ))}
          </div>
          
          <div className="tracking-details">
            <div className="detail-item">
              <span>Statut:</span>
              <span>{getDeliveryStatusLabel(tracking.status)}</span>
            </div>
            
            <div className="detail-item">
              <span>Livreur:</span>
              <span>{tracking.driver.name}</span>
            </div>
            
            <div className="detail-item">
              <span>Numéro de téléphone:</span>
              <span>{tracking.driver.phone}</span>
            </div>
            
            <div className="detail-item">
              <span>Heure estimée de livraison:</span>
              <span>{tracking.estimated_delivery_time}</span>
            </div>
            
            {tracking.location && (
              <div className="detail-item">
                <span>Localisation actuelle:</span>
                <span>{tracking.location.address}</span>
              </div>
            )}
            
            <div className="detail-item">
              <span>Date de création:</span>
              <span>{formatDate(tracking.created_at)}</span>
            </div>
          </div>
          
          {tracking.status === 'in_transit' && (
            <div className="live-tracking">
              <h3>Suivi en temps réel</h3>
              <div className="map-container">
                <Map 
                  latitude={tracking.location.latitude}
                  longitude={tracking.location.longitude}
                  zoom={14}
                />
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};
```

#### 1.6.2 Intégration avec ISMAIL Recouvrement

**Implémentation détaillée du système de suivi des créances**:

```ts
// src/services/api/creditRecoveryApi.ts
import { fetch } from 'whatwg-fetch';

export const creditRecoveryApi = {
  createClaim: async (data: any) => {
    const response = await fetch('/api/claims', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    
    return await response.json();
  },
  
  getClaim: async (claimId: string) => {
    const response = await fetch(`/api/claims/${claimId}`);
    return await response.json();
  },
  
  updateClaim: async (claimId: string, data: any) => {
    const response = await fetch(`/api/claims/${claimId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    
    return await response.json();
  },
  
  sendNotification: async (claimId: string, data: any) => {
    const response = await fetch(`/api/claims/${claimId}/notifications`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    
    return await response.json();
  }
};

// Dans le composant OrderDetails
import { creditRecoveryApi } from '../../services/api/creditRecoveryApi';
import { shopApi } from '../../services/api/shopApi';

export const OrderDetails: React.FC = () => {
  const [order, setOrder] = useState<any>(null);
  const [claim, setClaim] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  
  useEffect(() => {
    const orderId = new URLSearchParams(window.location.search).get('id');
    
    if (!orderId) {
      setError('ID de commande non fourni');
      setLoading(false);
      return;
    }
    
    const fetchOrder = async () => {
      try {
        setLoading(true);
        const orderData = await shopApi.getOrder(orderId);
        setOrder(orderData);
        
        // Vérifier si une créance existe pour cette commande
        if (orderData.payment_status === 'failed' || orderData.status === 'cancelled') {
          const claimData = await creditRecoveryApi.getClaimByOrder(orderId);
          setClaim(claimData);
        }
        
        setError('');
      } catch (error) {
        console.error('Erreur lors du chargement de la commande:', error);
        setError('Impossible de charger les détails de la commande');
      } finally {
        setLoading(false);
      }
    };
    
    fetchOrder();
  }, []);
  
  const handleCreateClaim = async () => {
    try {
      if (!order) return;
      
      const claimData = await creditRecoveryApi.createClaim({
        debtor_id: order.user_id,
        creditor_id: order.shop_id,
        amount: order.total_amount,
        description: `Créance pour la commande #${order.id}`,
        order_id: order.id
      });
      
      setClaim(claimData);
    } catch (error) {
      console.error('Erreur lors de la création de la créance:', error);
      setError('Impossible de créer la créance');
    }
  };
  
  const handleSendReminder = async () => {
    try {
      if (!claim) return;
      
      await creditRecoveryApi.sendNotification(claim.id, {
        type: 'reminder',
        content: `Rappel de paiement pour la commande #${order.id}`
      });
      
      // Actualiser les informations de la créance
      const updatedClaim = await creditRecoveryApi.getClaim(claim.id);
      setClaim(updatedClaim);
    } catch (error) {
      console.error('Erreur lors de l\'envoi du rappel:', error);
      setError('Impossible d\'envoyer le rappel');
    }
  };
  
  return (
    <div className="order-details">
      <h2>Détails de la commande #{order?.id}</h2>
      
      {loading ? (
        <Loader />
      ) : error ? (
        <div className="error-message">{error}</div>
      ) : (
        <>
          <div className="order-summary">
            <h3>Résumé de la commande</h3>
            <div className="summary-item">
              <span>Date:</span>
              <span>{formatDate(order.created_at)}</span>
            </div>
            <div className="summary-item">
              <span>Montant total:</span>
              <span>{currencyFormat(order.total_amount)}</span>
            </div>
            <div className="summary-item">
              <span>Statut de paiement:</span>
              <span className={`payment-status ${order.payment_status}`}>{order.payment_status}</span>
            </div>
            <div className="summary-item">
              <span>Statut de la commande:</span>
              <span className={`order-status ${order.status}`}>{order.status}</span>
            </div>
          </div>
          
          {order.payment_status === 'failed' && (
            <div className="credit-recovery">
              <h3>Suivi de la créance</h3>
              
              {claim ? (
                <div className="claim-details">
                  <div className="detail-item">
                    <span>Statut de la créance:</span>
                    <span className={`claim-status ${claim.status}`}>{claim.status}</span>
                  </div>
                  <div className="detail-item">
                    <span>Date de création:</span>
                    <span>{formatDate(claim.created_at)}</span>
                  </div>
                  <div className="detail-item">
                    <span>Dernière action:</span>
                    <span>{claim.last_action}</span>
                  </div>
                  <div className="detail-item">
                    <span>Prochaine étape:</span>
                    <span>{claim.next_step}</span>
                  </div>
                  
                  <div className="claim-actions">
                    <Button 
                      variant="primary" 
                      onClick={handleSendReminder}
                      disabled={claim.status === 'completed' || claim.status === 'canceled'}
                    >
                      Envoyer un rappel
                    </Button>
                    <Button 
                      variant="secondary" 
                      onClick={() => navigate(`/claims/${claim.id}`)}
                    >
                      Voir les détails de la créance
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="claim-actions">
                  <Button 
                    variant="primary" 
                    onClick={handleCreateClaim}
                    disabled={claim !== null}
                  >
                    Créer une créance
                  </Button>
                </div>
              )}
            </div>
          )}
          
          <div className="order-items">
            <h3>Articles commandés</h3>
            <div className="items-list">
              {order.items.map((item: any, index: number) => (
                <div key={index} className="item">
                  <div className="item-image">
                    <OptimizedImage 
                      src={item.product.images[0].url} 
                      width={100} 
                      height={100} 
                      alt={item.product.name}
                    />
                  </div>
                  <div className="item-details">
                    <h4>{item.product.name}</h4>
                    <div className="item-price">
                      {currencyFormat(item.price_per_unit)} x {item.quantity}
                    </div>
                    <div className="item-total">
                      Total: {currencyFormat(item.total_price)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </>
      )}
    </div>
  );
};
```

### 1.7 Points de Vigilance et Bonnes Pratiques

#### 1.7.1 Gestion des stocks et disponibilité

**Implémentation détaillée du système de gestion des stocks**:

```ts
// src/services/product/productService.ts
import { fetch } from 'whatwg-fetch';

export const productService = {
  // ... autres méthodes
  
  updateStock: async (productId: string, quantity: number, operation: 'add' | 'remove') => {
    const response = await fetch(`/api/products/${productId}/stock`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        quantity,
        operation
      })
    });
    
    return await response.json();
  },
  
  checkStockAvailability: async (productId: string, quantity: number) => {
    const response = await fetch(`/api/products/${productId}/stock/availability?quantity=${quantity}`);
    return await response.json();
  },
  
  getStockHistory: async (productId: string) => {
    const response = await fetch(`/api/products/${productId}/stock/history`);
    return await response.json();
  }
};

// Dans le composant ProductDetails
import { productService } from '../../services/product/productService';

export const ProductDetails: React.FC = () => {
  const [product, setProduct] = useState<any>(null);
  const [quantity, setQuantity] = useState(1);
  const [stockAvailable, setStockAvailable] = useState(true);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  
  useEffect(() => {
    const productId = new URLSearchParams(window.location.search).get('id');
    
    if (!productId) {
      setError('ID de produit non fourni');
      setLoading(false);
      return;
    }
    
    const fetchProduct = async () => {
      try {
        setLoading(true);
        const productData = await productService.getProduct(productId);
        setProduct(productData);
        
        // Vérifier la disponibilité du stock
        const availability = await productService.checkStockAvailability(productId, quantity);
        setStockAvailable(availability.available);
        
        setError('');
      } catch (error) {
        console.error('Erreur lors du chargement du produit:', error);
        setError('Impossible de charger les détails du produit');
      } finally {
        setLoading(false);
      }
    };
    
    fetchProduct();
  }, [quantity]);
  
  const handleAddToCart = async () => {
    if (!stockAvailable) {
      setError('Stock insuffisant pour cette quantité');
      return;
    }
    
    // Ajouter au panier
    cartService.addItem(product.id, quantity);
    // Mettre à jour le stock local
    setStockAvailable(false);
  };
  
  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity <= 0) {
      setQuantity(1);
      return;
    }
    
    setQuantity(newQuantity);
    
    // Vérifier la disponibilité du stock
    if (product) {
      productService.checkStockAvailability(product.id, newQuantity)
        .then(availability => setStockAvailable(availability.available))
        .catch(error => console.error('Erreur lors de la vérification du stock:', error));
    }
  };
  
  return (
    <div className="product-details">
      <h1>{product?.name}</h1>
      
      {loading ? (
        <Loader />
      ) : error ? (
        <div className="error-message">{error}</div>
      ) : (
        <>
          <div className="product-gallery">
            <ProductGallery images={product.images} />
          </div>
          
          <div className="product-info">
            <div className="product-price">
              {product.sale_price ? (
                <>
                  <span className="sale-price">{currencyFormat(product.sale_price)}</span>
                  <span className="original-price">{currencyFormat(product.price)}</span>
                </>
              ) : (
                <span>{currencyFormat(product.price)}</span>
              )}
            </div>
            
            <div className="product-description">
              <p>{product.description}</p>
            </div>
            
            <div className="product-specifications">
              <h3>Spécifications</h3>
              <div className="specifications-list">
                {Object.entries(product.specifications).map(([key, value]) => (
                  <div key={key} className="specification-item">
                    <span className="specification-key">{key}:</span>
                    <span className="specification-value">{value}</span>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="product-stock">
              <h3>Disponibilité</h3>
              <div className="stock-status">
                {stockAvailable ? (
                  <span className="in-stock">En stock ({product.stock} unités)</span>
                ) : (
                  <span className="out-of-stock">Stock insuffisant</span>
                )}
              </div>
              
              <div className="quantity-selector">
                <label htmlFor="quantity">Quantité:</label>
                <input
                  type="number"
                  id="quantity"
                  value={quantity}
                  min="1"
                  max={product.stock}
                  onChange={(e) => handleQuantityChange(parseInt(e.target.value))}
                />
              </div>
            </div>
            
            <div className="product-actions">
              <Button 
                variant="primary" 
                onClick={handleAddToCart}
                disabled={!stockAvailable || loading}
              >
                Ajouter au panier
              </Button>
              <Button 
                variant="secondary" 
                onClick={() => console.log('Acheter maintenant')}
              >
                Acheter maintenant
              </Button>
            </div>
          </div>
        </>
      )}
    </div>
  );
};
```

#### 1.7.2 Sécurité des transactions

**Implémentation détaillée du système de vérification à deux facteurs pour les transactions sensibles**:

```ts
// src/services/payment/paymentService.ts
import { fetch } from 'whatwg-fetch';

export const paymentService = {
  // ... autres méthodes
  
  initiateTransaction: async (data: any) => {
    const response = await fetch('/api/payments/initiate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    
    return await response.json();
  },
  
  verifyTransaction: async (transactionId: string, code: string) => {
    const response = await fetch(`/api/payments/verify`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        transaction_id: transactionId,
        verification_code: code
      })
    });
    
    return await response.json();
  },
  
  processPayment: async (data: any) => {
    const response = await fetch('/api/payments/process', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    
    return await response.json();
  }
};

// Dans le composant CheckoutForm
import { paymentService } from '../../services/payment/paymentService';
import { useAuth } from '../../hooks/useAuth';

export const CheckoutForm: React.FC = () => {
  const { user } = useAuth();
  const [step, setStep] = useState<'address' | 'payment' | 'verification' | 'success'>('address');
  const [transactionId, setTransactionId] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  const handleAddressSubmit = async (address: any) => {
    try {
      setLoading(true);
      const transactionData = await paymentService.initiateTransaction({
        user_id: user.id,
        items: cartItems,
        shipping_address: address,
        payment_method: 'wave'
      });
      
      setTransactionId(transactionData.id);
      setStep('verification');
      setError('');
    } catch (error) {
      console.error('Erreur lors de l\'initiation du paiement:', error);
      setError('Impossible d\'initier le paiement');
    } finally {
      setLoading(false);
    }
  };
  
  const handleVerificationSubmit = async () => {
    try {
      setLoading(true);
      const verificationResult = await paymentService.verifyTransaction(transactionId, verificationCode);
      
      if (verificationResult.verified) {
        const paymentResult = await paymentService.processPayment({
          transaction_id: transactionId,
          verification_code: verificationCode
        });
        
        if (paymentResult.success) {
          setStep('success');
          clearCart();
          navigate('/orders/success');
        } else {
          setError('Échec du paiement');
        }
      } else {
        setError('Code de vérification invalide');
      }
    } catch (error) {
      console.error('Erreur lors de la vérification du paiement:', error);
      setError('Impossible de vérifier le paiement');
    } finally {
      setLoading(false);
    }
  };
  
  const handleResendCode = async () => {
    try {
      setLoading(true);
      await paymentService.resendVerificationCode(transactionId);
      setError('');
    } catch (error) {
      console.error('Erreur lors de la réémission du code:', error);
      setError('Impossible de réémettre le code');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="checkout-form">
      {step === 'address' && (
        <AddressForm onSubmit={handleAddressSubmit} />
      )}
      
      {step === 'verification' && (
        <VerificationForm
          transactionId={transactionId}
          verificationCode={verificationCode}
          onCodeChange={setVerificationCode}
          onSubmit={handleVerificationSubmit}
          onResendCode={handleResendCode}
          loading={loading}
          error={error}
        />
      )}
      
      {step === 'success' && (
        <SuccessMessage />
      )}
    </div>
  );
};

// Composant VerificationForm
export const VerificationForm: React.FC<{
  transactionId: string;
  verificationCode: string;
  onCodeChange: (code: string) => void;
  onSubmit: () => void;
  onResendCode: () => void;
  loading: boolean;
  error: string;
}> = ({
  transactionId,
  verificationCode,
  onCodeChange,
  onSubmit,
  onResendCode,
  loading,
  error
}) => {
  return (
    <div className="verification-form">
      <h2>Vérification à deux facteurs</h2>
      
      <p>Un code de vérification a été envoyé à votre téléphone. Veuillez le saisir ci-dessous.</p>
      
      {error && <div className="error-message">{error}</div>}
      
      <div className="form-group">
        <label htmlFor="verification_code">Code de vérification</label>
        <input
          type="text"
          id="verification_code"
          value={verificationCode}
          onChange={(e) => onCodeChange(e.target.value)}
          maxLength={6}
          required
        />
      </div>
      
      <div className="verification-actions">
        <Button 
          variant="primary" 
          onClick={onSubmit}
          disabled={loading || verificationCode.length < 6}
        >
          {loading ? 'Vérification en cours...' : 'Vérifier'}
        </Button>
        
        <Button 
          variant="secondary" 
          onClick={onResendCode}
          disabled={loading}
        >
          {loading ? 'Envoi en cours...' : 'Renvoyer le code'}
        </Button>
      </div>
    </div>
  );
};

// Dans le service de notification
export const notificationService = {
  sendVerificationCode: async (userId: string, phoneNumber: string) => {
    const response = await fetch('/api/notifications/verification-code', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        user_id: userId,
        phone_number: phoneNumber
      })
    });
    
    return await response.json();
  },
  
  resendVerificationCode: async (transactionId: string) => {
    const response = await fetch(`/api/notifications/resend-verification-code`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        transaction_id: transactionId
      })
    });
    
    return await response.json();
  }
};
```

## 2. ISMAIL Services (Prestations Professionnelles)

### 2.1 Architecture Technique

#### 2.1.1 Schéma d'Architecture Global

```
+---------------------+      +---------------------+      +---------------------+
| Frontend (React)    |<---->| API Gateway         |<---->| ISMAIL Core         |
| (PWA)               |      | (Netlify)           |      | (Services transverses) |
+---------------------+      +---------------------+      +---------------------+
       ▲                          ▲                          ▲
       |                          |                          |
       |                          |                          |
       ▼                          ▼                          ▼
+----------------------+      +---------------------+      +---------------------+
| Services Module      |<---->| Event Bus           |<---->| Data Pipeline       |
| (Microservice)       |      | (Kafka)             |      | (Stream Processing) |
+----------------------+      +---------------------+      +---------------------+
       ▲                          ▲                          ▲
       |                          |                          |
       |                          |                          |
       ▼                          ▼                          ▼
+----------------------+      +---------------------+      +---------------------+
| Base de Données      |      | Services Externes   |      | Base de Données     |
| (Neon PostgreSQL)    |      | (Wave, Payment)     |      | (PostgreSQL Neon)   |
+----------------------+      +---------------------+      +---------------------+
```

#### 2.1.2 Structure de Dossier du Module

```
ismail-services/
├── src/
│   ├── components/
│   │   ├── service/
│   │   │   ├── ServiceCard.tsx
│   │   │   ├── ServiceDetails.tsx
│   │   │   ├── ServiceRating.tsx
│   │   │   └── ServiceGallery.tsx
│   │   ├── provider/
│   │   │   ├── ProviderProfile.tsx
│   │   │   ├── ProviderPortfolio.tsx
│   │   │   └── ProviderRating.tsx
│   │   ├── request/
│   │   │   ├── RequestForm.tsx
│   │   │   ├── RequestList.tsx
│   │   │   └── RequestDetails.tsx
│   │   ├── quotation/
│   │   │   ├── QuotationCard.tsx
│   │   │   ├── QuotationDetails.tsx
│   │   │   └── QuotationForm.tsx
│   │   ├── common/
│   │   │   ├── Button.tsx
│   │   │   ├── Input.tsx
│   │   │   ├── Rating.tsx
│   │   │   └── Loader.tsx
│   ├── services/
│   │   ├── api/
│   │   │   ├── servicesApi.ts
│   │   │   ├── providersApi.ts
│   │   │   └── paymentsApi.ts
│   │   ├── request/
│   │   │   ├── requestService.ts
│   │   │   └── requestReducer.ts
│   │   ├── provider/
│   │   │   ├── providerService.ts
│   │   │   └── providerFilterService.ts
│   │   └── quotation/
│   │       ├── quotationService.ts
│   │       └── quotationValidation.ts
│   ├── hooks/
│   │   ├── useRequest.ts
│   │   ├── useProvider.ts
│   │   └── useQuotation.ts
│   ├── pages/
│   │   ├── Home.tsx
│   │   ├── ServiceList.tsx
│   │   ├── ServiceDetail.tsx
│   │   ├── ProviderList.tsx
│   │   ├── ProviderDetail.tsx
│   │   ├── RequestForm.tsx
│   │   ├── RequestList.tsx
│   │   ├── QuotationList.tsx
│   │   ├── QuotationDetail.tsx
│   │   └── Payment.tsx
│   ├── contexts/
│   │   ├── RequestContext.tsx
│   │   ├── AuthContext.tsx
│   │   └── ProviderContext.tsx
│   ├── utils/
│   │   ├── currency.ts
│   │   ├── validation.ts
│   │   ├── geolocation.ts
│   │   └── image.ts
│   ├── styles/
│   │   ├── global.scss
│   │   ├── theme.scss
│   │   └── components/
│   │       ├── button.scss
│   │       ├── card.scss
│   │       └── form.scss
│   └── app.tsx
├── netlify.toml
├── package.json
└── tsconfig.json
```

### 2.2 Structure de Données

#### 2.2.1 Schéma de Base de Données

```
service_providers
- id(UUID) PRIMARY KEY
- user_id(UUID) REFERENCES users(id) NOT NULL
- business_name(varchar(255)) NOT NULL
- category_id(UUID) REFERENCES service_categories(id) NOT NULL
- subcategory_id(UUID) REFERENCES service_subcategories(id)
- description(text) NOT NULL
- working_hours(jsonb) NOT NULL
- address(jsonb) NOT NULL
- rating(numeric(3,2)) DEFAULT 0.0
- reviews_count(int) DEFAULT 0
- status(varchar(20)) DEFAULT 'pending' CHECK(status IN ('pending', 'active', 'suspended', 'deleted'))
- created_at(timestamptz) DEFAULT CURRENT_TIMESTAMP
- updated_at(timestamptz) DEFAULT CURRENT_TIMESTAMP

service_categories
- id(UUID) PRIMARY KEY
- name(varchar(100)) NOT NULL
- description(text)
- parent_id(UUID) REFERENCES service_categories(id)
- created_at(timestamptz) DEFAULT CURRENT_TIMESTAMP
- updated_at(timestamptz) DEFAULT CURRENT_TIMESTAMP

service_subcategories
- id(UUID) PRIMARY KEY
- name(varchar(100)) NOT NULL
- category_id(UUID) REFERENCES service_categories(id) NOT NULL
- created_at(timestamptz) DEFAULT CURRENT_TIMESTAMP
- updated_at(timestamptz) DEFAULT CURRENT_TIMESTAMP

service_requests
- id(UUID) PRIMARY KEY
- user_id(UUID) REFERENCES users(id) NOT NULL
- provider_id(UUID) REFERENCES service_providers(id)
- category_id(UUID) REFERENCES service_categories(id) NOT NULL
- subcategory_id(UUID) REFERENCES service_subcategories(id)
- description(text) NOT NULL
- location(jsonb) NOT NULL
- status(varchar(20)) DEFAULT 'pending' CHECK(status IN ('pending', 'in_progress', 'completed', 'canceled', 'rejected'))
- created_at(timestamptz) DEFAULT CURRENT_TIMESTAMP
- updated_at(timestamptz) DEFAULT CURRENT_TIMESTAMP

quotations
- id(UUID) PRIMARY KEY
- request_id(UUID) REFERENCES service_requests(id) NOT NULL
- provider_id(UUID) REFERENCES service_providers(id) NOT NULL
- amount(numeric(10,2)) NOT NULL
- validity(timestamptz) NOT NULL
- status(varchar(20)) DEFAULT 'pending' CHECK(status IN ('pending', 'accepted', 'rejected', 'expired'))
- created_at(timestamptz) DEFAULT CURRENT_TIMESTAMP
- updated_at(timestamptz) DEFAULT CURRENT_TIMESTAMP

transactions
- id(UUID) PRIMARY KEY
- quotation_id(UUID) REFERENCES quotations(id) NOT NULL
- amount(numeric(10,2)) NOT NULL
- payment_method(varchar(50)) NOT NULL
- status(varchar(20)) DEFAULT 'pending' CHECK(status IN ('pending', 'completed', 'failed', 'refunded'))
- created_at(timestamptz) DEFAULT CURRENT_TIMESTAMP
- updated_at(timestamptz) DEFAULT CURRENT_TIMESTAMP

reviews
- id(UUID) PRIMARY KEY
- request_id(UUID) REFERENCES service_requests(id) NOT NULL
- user_id(UUID) REFERENCES users(id) NOT NULL
- provider_id(UUID) REFERENCES service_providers(id) NOT NULL
- rating(int) NOT NULL CHECK(rating BETWEEN 1 AND 5)
- comment(text)
- status(varchar(20)) DEFAULT 'pending' CHECK(status IN ('pending', 'approved', 'rejected'))
- created_at(timestamptz) DEFAULT CURRENT_TIMESTAMP
- updated_at(timestamptz) DEFAULT CURRENT_TIMESTAMP
```

#### 2.2.2 Exemple de Structure de Données pour un Fournisseur de Services

```json
{
  "id": "a1b2c3d4-5678-90ab-cdef-1234567890ab",
  "user_id": "e1f2a3b4-5678-90ab-cdef-1234567890cd",
  "business_name": "Tech Solutions Dakar",
  "category_id": "f5a6b7c8-d9e0-1234-5678-90abcdef1234",
  "subcategory_id": "g5h6i7j8-k9l0-1234-5678-90abcdef5678",
  "description": "Entreprise de développement web et applications mobiles avec 5 ans d'expérience dans le secteur",
  "working_hours": [
    {
      "day": "Monday",
      "open": "08:00",
      "close": "18:00"
    },
    {
      "day": "Tuesday",
      "open": "08:00",
      "close": "18:00"
    },
    {
      "day": "Wednesday",
      "open": "08:00",
      "close": "18:00"
    },
    {
      "day": "Thursday",
      "open": "08:00",
      "close": "18:00"
    },
    {
      "day": "Friday",
      "open": "08:00",
      "close": "17:00"
    },
    {
      "day": "Saturday",
      "open": "09:00",
      "close": "13:00"
    },
    {
      "day": "Sunday",
      "open": "closed",
      "close": "closed"
    }
  ],
  "address": {
    "street": "Avenue de la République",
    "city": "Dakar",
    "postal_code": "10000",
    "country": "SN",
    "latitude": 14.7167,
    "longitude": -17.4677
  },
  "rating": 4.8,
  "reviews_count": 42,
  "status": "active",
  "created_at": "2023-01-15T12:30:00Z",
  "updated_at": "2023-01-15T12:30:00Z"
}
```

### 2.3 APIs Clés

#### 2.3.1 Gestion des Fournisseurs de Services

```
POST /api/providers
```

**Description**: Création d'un nouveau fournisseur de services

**Requête**:
```json
{
  "business_name": "Tech Solutions Dakar",
  "category_id": "f5a6b7c8-d9e0-1234-5678-90abcdef1234",
  "subcategory_id": "g5h6i7j8-k9l0-1234-5678-90abcdef5678",
  "description": "Entreprise de développement web et applications mobiles avec 5 ans d'expérience dans le secteur",
  "working_hours": [
    {
      "day": "Monday",
      "open": "08:00",
      "close": "18:00"
    },
    {
      "day": "Tuesday",
      "open": "08:00",
      "close": "18:00"
    },
    {
      "day": "Wednesday",
      "open": "08:00",
      "close": "18:00"
    },
    {
      "day": "Thursday",
      "open": "08:00",
      "close": "18:00"
    },
    {
      "day": "Friday",
      "open": "08:00",
      "close": "17:00"
    },
    {
      "day": "Saturday",
      "open": "09:00",
      "close": "13:00"
    },
    {
      "day": "Sunday",
      "open": "closed",
      "close": "closed"
    }
  ],
  "address": {
    "street": "Avenue de la République",
    "city": "Dakar",
    "postal_code": "10000",
    "country": "SN",
    "latitude": 14.7167,
    "longitude": -17.4677
  }
}
```

**Réponse**:
```json
{
  "id": "a1b2c3d4-5678-90ab-cdef-1234567890ab",
  "business_name": "Tech Solutions Dakar",
  "category_id": "f5a6b7c8-d9e0-1234-5678-90abcdef1234",
  "subcategory_id": "g5h6i7j8-k9l0-1234-5678-90abcdef5678",
  "description": "Entreprise de développement web et applications mobiles avec 5 ans d'expérience dans le secteur",
  "working_hours": [
    {
      "day": "Monday",
      "open": "08:00",
      "close": "18:00"
    },
    {
      "day": "Tuesday",
      "open": "08:00",
      "close": "18:00"
    },
    {
      "day": "Wednesday",
      "open": "08:00",
      "close": "18:00"
    },
    {
      "day": "Thursday",
      "open": "08:00",
      "close": "18:00"
    },
    {
      "day": "Friday",
      "open": "08:00",
      "close": "17:00"
    },
    {
      "day": "Saturday",
      "open": "09:00",
      "close": "13:00"
    },
    {
      "day": "Sunday",
      "open": "closed",
      "close": "closed"
    }
  ],
  "address": {
    "street": "Avenue de la République",
    "city": "Dakar",
    "postal_code": "10000",
    "country": "SN",
    "latitude": 14.7167,
    "longitude": -17.4677
  },
  "rating": 4.8,
  "reviews_count": 42,
  "status": "active",
  "created_at": "2023-01-15T12:30:00Z",
  "updated_at": "2023-01-15T12:30:00Z"
}
```

```
GET /api/providers/{id}
```

**Description**: Récupération des détails d'un fournisseur de services

**Réponse**:
```json
{
  "id": "a1b2c3d4-5678-90ab-cdef-1234567890ab",
  "business_name": "Tech Solutions Dakar",
  "category_id": "f5a6b7c8-d9e0-1234-5678-90abcdef1234",
  "subcategory_id": "g5h6i7j8-k9l0-1234-5678-90abcdef5678",
  "description": "Entreprise de développement web et applications mobiles avec 5 ans d'expérience dans le secteur",
  "working_hours": [
    {
      "day": "Monday",
      "open": "08:00",
      "close": "18:00"
    },
    {
      "day": "Tuesday",
      "open": "08:00",
      "close": "18:00"
    },
    {
      "day": "Wednesday",
      "open": "08:00",
      "close": "18:00"
    },
    {
      "day": "Thursday",
      "open": "08:00",
      "close": "18:00"
    },
    {
      "day": "Friday",
      "open": "08:00",
      "close": "17:00"
    },
    {
      "day": "Saturday",
      "open": "09:00",
      "close": "13:00"
    },
    {
      "day": "Sunday",
      "open": "closed",
      "close": "closed"
    }
  ],
  "address": {
    "street": "Avenue de la République",
    "city": "Dakar",
    "postal_code": "10000",
    "country": "SN",
    "latitude": 14.7167,
    "longitude": -17.4677
  },
  "rating": 4.8,
  "reviews_count": 42,
  "status": "active",
  "created_at": "2023-01-15T12:30:00Z",
  "updated_at": "2023-01-15T12:30:00Z"
}
```

#### 2.3.2 Gestion des Demandes de Services

```
POST /api/requests
```

**Description**: Création d'une nouvelle demande de service

**Requête**:
```json
{
  "user_id": "e1f2a3b4-5678-90ab-cdef-1234567890cd",
  "category_id": "f5a6b7c8-d9e0-1234-5678-90abcdef1234",
  "subcategory_id": "g5h6i7j8-k9l0-1234-5678-90abcdef5678",
  "description": "Besoin de développement d'une application mobile pour une entreprise de transport",
  "location": {
    "street": "Avenue de la République",
    "city": "Dakar",
    "postal_code": "10000",
    "country": "SN",
    "latitude": 14.7167,
    "longitude": -17.4677
  }
}
```

**Réponse**:
```json
{
  "id": "c3d4e5f6-7890-1234-5678-90abcdef1234",
  "user_id": "e1f2a3b4-5678-90ab-cdef-1234567890cd",
  "category_id": "f5a6b7c8-d9e0-1234-5678-90abcdef1234",
  "subcategory_id": "g5h6i7j8-k9l0-1234-5678-90abcdef5678",
  "description": "Besoin de développement d'une application mobile pour une entreprise de transport",
  "location": {
    "street": "Avenue de la République",
    "city": "Dakar",
    "postal_code": "10000",
    "country": "SN",
    "latitude": 14.7167,
    "longitude": -17.4677
  },
  "status": "pending",
  "created_at": "2023-01-15T12:30:00Z",
  "updated_at": "2023-01-15T12:30:00Z"
}
```

### 2.4 Fonctionnalités Clés

#### 2.4.1 Interface de Création de Demandes de Services

**Implémentation détaillée**:

```tsx
// src/components/request/RequestForm.tsx
import React, { useState, useEffect } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { Input } from '../common/Input';
import { Button } from '../common/Button';
import { useNavigate } from 'react-router-dom';
import { requestService } from '../../services/request/requestService';
import { validateDescription } from '../../utils/validation';

interface RequestFormProps {
  onSuccess?: (request: any) => void;
}

export const RequestForm: React.FC<RequestFormProps> = ({ onSuccess }) => {
  const { user } = useAuth();
  const navigate = useNavigate();
  
  const [category, setCategory] = useState('');
  const [subcategory, setSubcategory] = useState('');
  const [description, setDescription] = useState('');
  const [location, setLocation] = useState({
    street: '',
    city: '',
    postal_code: '',
    country: 'SN',
    latitude: 0,
    longitude: 0
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  useEffect(() => {
    if (user) {
      setLocation({
        street: user.address?.street || '',
        city: user.address?.city || '',
        postal_code: user.address?.postal_code || '',
        country: user.address?.country || 'SN',
        latitude: user.address?.latitude || 0,
        longitude: user.address?.longitude || 0
      });
    }
  }, [user]);
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation de la description
    const descriptionValidation = validateDescription(description);
    if (!descriptionValidation.valid) {
      setError(descriptionValidation.message);
      return;
    }
    
    setLoading(true);
    setError('');
    
    try {
      const requestData = await requestService.createRequest({
        user_id: user.id,
        category_id: category,
        subcategory_id: subcategory,
        description,
        location
      });
      
      if (onSuccess) {
        onSuccess(requestData);
      } else {
        navigate(`/requests/${requestData.id}`);
      }
    } catch (error) {
      console.error('Erreur lors de la création de la demande:', error);
      setError('Une erreur est survenue lors de la création de la demande. Veuillez réessayer.');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className="request-form">
      <h2>Décrivez votre besoin</h2>
      
      <div className="form-group">
        <label>Catégorie</label>
        <select
          value={category}
          onChange={(e) => setCategory(e.target.value)}
          required
        >
          <option value="">Sélectionnez une catégorie</option>
          <option value="development">Développement web et mobile</option>
          <option value="design">Design graphique</option>
          <option value="marketing">Marketing et communication</option>
          <option value="consulting">Consulting et conseil</option>
          <option value="other">Autre</option>
        </select>
      </div>
      
      <div className="form-group">
        <label>Sous-catégorie</label>
        <select
          value={subcategory}
          onChange={(e) => setSubcategory(e.target.value)}
          required
        >
          <option value="">Sélectionnez une sous-catégorie</option>
          <option value="web">Développement web</option>
          <option value="mobile">Développement mobile</option>
          <option value="ui">Design d'interface utilisateur</option>
          <option value="ux">Design d'expérience utilisateur</option>
          <option value="social">Marketing sur les réseaux sociaux</option>
          <option value="seo">SEO et référencement</option>
        </select>
      </div>
      
      <div className="form-group">
        <label>Description détaillée</label>
        <textarea
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          rows={5}
          required
        />
        {error && <div className="error-message">{error}</div>}
      </div>
      
      <h2>Localisation</h2>
      
      <div className="form-group">
        <Input
          label="Rue"
          name="street"
          value={location.street}
          onChange={(e) => setLocation({...location, street: e.target.value})}
          required
        />
      </div>
      
      <div className="form-group">
        <Input
          label="Ville"
          name="city"
          value={location.city}
          onChange={(e) => setLocation({...location, city: e.target.value})}
          required
        />
      </div>
      
      <div className="form-group">
        <Input
          label="Code postal"
          name="postal_code"
          value={location.postal_code}
          onChange={(e) => setLocation({...location, postal_code: e.target.value})}
          required
        />
      </div>
      
      <div className="form-group">
        <Input
          label="Pays"
          name="country"
          value={location.country}
          onChange={(e) => setLocation({...location, country: e.target.value})}
          required
        />
      </div>
      
      <Button 
        type="submit" 
        variant="primary" 
        disabled={loading}
      >
        {loading ? 'Création en cours...' : 'Créer la demande'}
      </Button>
    </form>
  );
};
```

**Optimisation pour les réseaux mobiles africains**:

```tsx
// src/components/request/RequestForm.tsx
import React, { useState, useEffect } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { Input } from '../common/Input';
import { Button } from '../common/Button';
import { useNavigate } from 'react-router-dom';
import { requestService } from '../../services/request/requestService';
import { validateDescription } from '../../utils/validation';

// Détection du type de réseau
const detectNetworkType = (): '2g' | '3g' | '4g' => {
  if ('connection' in navigator) {
    const connection = navigator.connection;
    if (connection) {
      if (connection.effectiveType === '2g') {
        return '2g';
      } else if (connection.effectiveType === '3g') {
        return '3g';
      }
    }
  }
  return '4g';
};

export const RequestForm: React.FC<RequestFormProps> = ({ onSuccess }) => {
  // ... autres variables
  
  useEffect(() => {
    // Détecter le type de réseau
    const networkType = detectNetworkType();
    
    // Si l'utilisateur est sur un réseau 2G, charger les données de base
    if (networkType === '2g') {
      // Charger seulement les catégories principales
      loadCategories('basic');
    } else {
      // Charger toutes les catégories
      loadCategories('full');
    }
  }, []);
  
  const loadCategories = async (type: 'basic' | 'full') => {
    try {
      const categories = await categoryService.getCategories(type);
      setCategories(categories);
    } catch (error) {
      console.error('Erreur lors du chargement des catégories:', error);
    }
  };
  
  // ... reste du composant
};
```

#### 2.4.2 Système de Devis et Paiement

**Implémentation détaillée du système de devis**:

```tsx
// src/components/quotation/QuotationForm.tsx
import React, { useState, useEffect } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { Input } from '../common/Input';
import { Button } from '../common/Button';
import { useNavigate } from 'react-router-dom';
import { quotationService } from '../../services/quotation/quotationService';
import { currencyFormat } from '../../utils/currency';

interface QuotationFormProps {
  requestId: string;
  onQuotationCreated?: (quotation: any) => void;
}

export const QuotationForm: React.FC<QuotationFormProps> = ({ requestId, onQuotationCreated }) => {
  const { user } = useAuth();
  const navigate = useNavigate();
  
  const [amount, setAmount] = useState('');
  const [validity, setValidity] = useState('7');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!amount || parseFloat(amount) <= 0) {
      setError('Le montant doit être supérieur à zéro');
      return;
    }
    
    setLoading(true);
    setError('');
    
    try {
      const quotationData = await quotationService.createQuotation({
        request_id: requestId,
        provider_id: user.id,
        amount: parseFloat(amount),
        validity: new Date(Date.now() + parseInt(validity) * 24 * 60 * 60 * 1000)
      });
      
      if (onQuotationCreated) {
        onQuotationCreated(quotationData);
      } else {
        navigate(`/quotations/${quotationData.id}`);
      }
    } catch (error) {
      console.error('Erreur lors de la création du devis:', error);
      setError('Une erreur est survenue lors de la création du devis. Veuillez réessayer.');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className="quotation-form">
      <h2>Proposez un devis</h2>
      
      <div className="form-group">
        <label>Montant (F CFA)</label>
        <Input
          type="number"
          value={amount}
          onChange={(e) => setAmount(e.target.value)}
          placeholder="Entrez le montant"
          required
        />
      </div>
      
      <div className="form-group">
        <label>Validité du devis</label>
        <select
          value={validity}
          onChange={(e) => setValidity(e.target.value)}
          required
        >
          <option value="7">7 jours</option>
          <option value="14">14 jours</option>
          <option value="30">30 jours</option>
          <option value="60">60 jours</option>
          <option value="90">90 jours</option>
        </select>
      </div>
      
      <div className="form-group">
        <label>Total</label>
        <div className="total-amount">
          {amount ? currencyFormat(parseFloat(amount)) : '0 F CFA'}
        </div>
      </div>
      
      {error && <div className="error-message">{error}</div>}
      
      <Button 
        type="submit" 
        variant="primary" 
        disabled={loading}
      >
        {loading ? 'Création en cours...' : 'Créer le devis'}
      </Button>
    </form>
  );
};
```

**Implémentation détaillée du système de paiement**:

```tsx
// src/components/payment/PaymentForm.tsx
import React, { useState, useEffect } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { Button } from '../common/Button';
import { useNavigate } from 'react-router-dom';
import { paymentService } from '../../services/payment/paymentService';
import { currencyFormat } from '../../utils/currency';

interface PaymentFormProps {
  quotationId: string;
  amount: number;
  onSuccess?: () => void;
}

export const PaymentForm: React.FC<PaymentFormProps> = ({ quotationId, amount, onSuccess }) => {
  const { user } = useAuth();
  const navigate = useNavigate();
  
  const [paymentMethod, setPaymentMethod] = useState('wave');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  useEffect(() => {
    // Si l'utilisateur est connecté, définir la méthode de paiement par défaut
    if (user) {
      setPaymentMethod('wave');
    }
  }, [user]);
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    setLoading(true);
    setError('');
    
    try {
      // Initialiser le paiement
      const paymentData = await paymentService.initiatePayment({
        quotation_id: quotationId,
        amount,
        payment_method: paymentMethod,
        user_id: user.id
      });
      
      // Rediriger vers la page de paiement Wave
      if (paymentMethod === 'wave') {
        window.location.href = paymentData.url;
      } else {
        // Pour d'autres méthodes de paiement
        onSuccess?.();
        navigate('/payments/success');
      }
    } catch (error) {
      console.error('Erreur lors de l\'initialisation du paiement:', error);
      setError('Une erreur est survenue lors de l\'initialisation du paiement. Veuillez réessayer.');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className="payment-form">
      <h2>Choisissez votre méthode de paiement</h2>
      
      <div className="payment-methods">
        <div className="payment-method">
          <input
            type="radio"
            id="wave"
            name="payment_method"
            value="wave"
            checked={paymentMethod === 'wave'}
            onChange={() => setPaymentMethod('wave')}
          />
          <label htmlFor="wave">Wave Mobile Money</label>
        </div>
        
        <div className="payment-method">
          <input
            type="radio"
            id="card"
            name="payment_method"
            value="card"
            checked={paymentMethod === 'card'}
            onChange={() => setPaymentMethod('card')}
          />
          <label htmlFor="card">Carte bancaire</label>
        </div>
        
        <div className="payment-method">
          <input
            type="radio"
            id="cash"
            name="payment_method"
            value="cash"
            checked={paymentMethod === 'cash'}
            onChange={() => setPaymentMethod('cash')}
          />
          <label htmlFor="cash">Paiement en espèces</label>
        </div>
      </div>
      
      <div className="payment-summary">
        <h3>Résumé du paiement</h3>
        <div className="summary-item">
          <span>Montant:</span>
          <span>{currencyFormat(amount)}</span>
        </div>
        <div className="summary-item">
          <span>Méthode de paiement:</span>
          <span>{paymentMethod === 'wave' ? 'Wave Mobile Money' : paymentMethod === 'card' ? 'Carte bancaire' : 'Paiement en espèces'}</span>
        </div>
      </div>
      
      {error && <div className="error-message">{error}</div>}
      
      <Button 
        type="submit" 
        variant="primary" 
        disabled={loading}
      >
        {loading ? 'Traitement en cours...' : `Payer ${currencyFormat(amount)}`}
      </Button>
    </form>
  );
};
```

### 2.5 Spécificités pour le Marché Africain

#### 2.5.1 Adaptation aux réseaux mobiles africains

**Optimisation des images pour les réseaux 2G/3G**:

```ts
// src/utils/image.ts
export const optimizeForMobileNetwork = (image: File, networkType: '2g' | '3g' | '4g'): Blob => {
  let width = 800;
  let height = 800;
  let quality = 85;
  
  switch (networkType) {
    case '2g':
      width = 400;
      height = 400;
      quality = 65;
      break;
    case '3g':
      width = 600;
      height = 600;
      quality = 75;
      break;
    case '4g':
      width = 800;
      height = 800;
      quality = 85;
      break;
  }
  
  // Créer un canvas pour redimensionner et compresser l'image
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  
  const img = new Image();
  img.src = URL.createObjectURL(image);
  
  return new Promise((resolve) => {
    img.onload = () => {
      canvas.width = width;
      canvas.height = height;
      ctx?.drawImage(img, 0, 0, width, height);
      
      canvas.toBlob((blob) => {
        if (blob) {
          resolve(blob);
        } else {
          resolve(image);
        }
      }, 'image/webp', quality / 100);
    };
  });
};

// Dans le service de chargement des photos
export const loadProviderPhotos = async (provider: any, networkType: '2g' | '3g' | '4g') => {
  // Si l'utilisateur est sur un réseau 2G, optimiser les images
  if (networkType === '2g') {
    const optimizedPhotos = await Promise.all(
      provider.photos.map(async (photo: any) => {
        const response = await fetch(photo.url);
        const blob = await response.blob();
        const optimizedBlob = await optimizeForMobileNetwork(blob, networkType);
        return {
          ...photo,
          url: URL.createObjectURL(optimizedBlob)
        };
      })
    );
    
    return {
      ...provider,
      photos: optimizedPhotos
    };
  }
  
  return provider;
};

// Dans le composant ProviderPortfolio
export const ProviderPortfolio: React.FC = () => {
  // Détection du type de réseau
  const [networkType, setNetworkType] = useState<'2g' | '3g' | '4g'>('4g');
  
  useEffect(() => {
    // Utilisation de l'API Network Information pour détecter le type de réseau
    if ('connection' in navigator) {
      const connection = navigator.connection;
      if (connection) {
        if (connection.effectiveType === '2g') {
          setNetworkType('2g');
        } else if (connection.effectiveType === '3g') {
          setNetworkType('3g');
        } else {
          setNetworkType('4g');
        }
      }
    }
  }, []);
  
  // Utilisation de la détection de réseau pour charger les images optimisées
  useEffect(() => {
    if (provider) {
      loadProviderPhotos(provider, networkType)
        .then(optimizedProvider => setProvider(optimizedProvider))
        .catch(error => console.error('Erreur lors du chargement des photos:', error));
    }
  }, [networkType, provider]);
  
  // ... reste du composant
};
```

#### 2.5.2 Gestion des devises locales

**Implémentation détaillée du système de conversion de devises**:

```ts
// src/utils/currency.ts
interface Currency {
  code: string;
  symbol: string;
  name: string;
  rate: number;
}

const CURRENCIES: Record<string, Currency> = {
  'XOF': { code: 'XOF', symbol: 'FCFA', name: 'Franc CFA', rate: 1.0 },
  'XAF': { code: 'XAF', symbol: 'FCFA', name: 'Franc CFA', rate: 1.0 },
  'NGN': { code: 'NGN', symbol: '₦', name: 'Naira', rate: 650.0 },
  'KES': { code: 'KES', symbol: 'KSh', name: 'Shilling kenyan', rate: 120.0 },
  'UGX': { code: 'UGX', symbol: 'USh', name: 'Shilling ougandais', rate: 3700.0 },
  'ZAR': { code: 'ZAR', symbol: 'R', name: 'Rand', rate: 18.0 },
  'GHS': { code: 'GHS', symbol: 'GH¢', name: 'Cedi ghanéen', rate: 8.0 },
  'MZN': { code: 'MZN', symbol: 'MT', name: 'Metical', rate: 65.0 },
  'USD': { code: 'USD', symbol: '$', name: 'Dollar américain', rate: 600.0 },
  'EUR': { code: 'EUR', symbol: '€', name: 'Euro', rate: 650.0 }
};

export const getCurrencyRate = (code: string): number => {
  const currency = CURRENCIES[code];
  return currency ? currency.rate : 1.0;
};

export const convertCurrency = (amount: number, from: string, to: string): number => {
  const fromRate = getCurrencyRate(from);
  const toRate = getCurrencyRate(to);
  
  // Conversion via USD pour éviter les erreurs de calcul
  const usdAmount = amount / fromRate;
  return usdAmount * toRate;
};

export const currencyFormat = (amount: number, currency: string = 'XOF', locale: string = 'fr-CD'): string => {
  const currencyRate = getCurrencyRate(currency);
  const convertedAmount = amount * currencyRate;
  
  // Formatage selon la devise
  const options: Intl.NumberFormatOptions = {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  };
  
  return new Intl.NumberFormat(locale, options).format(convertedAmount);
};

export const currencySymbol = (currency: string = 'XOF'): string => {
  const currencyData = CURRENCIES[currency];
  return currencyData ? currencyData.symbol : 'FCFA';
};

// Dans le service de paiement
export const paymentService = {
  initiatePayment: async (data: any) => {
    // Déterminer la devise en fonction du pays de l'utilisateur
    const userCountry = data.location?.country;
    let currency = 'XOF';
    
    switch (userCountry) {
      case 'NG':
        currency = 'NGN';
        break;
      case 'KE':
        currency = 'KES';
        break;
      case 'UG':
        currency = 'UGX';
        break;
      case 'ZA':
        currency = 'ZAR';
        break;
      case 'GH':
        currency = 'GHS';
        break;
      case 'MZ':
        currency = 'MZN';
        break;
      default:
        currency = 'XOF';
    }
    
    // Convertir le montant en devise locale
    const convertedAmount = convertCurrency(data.amount, 'XOF', currency);
    
    const response = await fetch('/api/payments/initiate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Currency': currency
      },
      body: JSON.stringify({
        ...data,
        amount: convertedAmount,
        currency
      })
    });
    
    return await response.json();
  },
  
  processPayment: async (data: any) => {
    const response = await fetch('/api/payments/process', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    
    return await response.json();
  }
};
```

### 2.6 Intégration avec les Autres Modules

#### 2.6.1 Intégration avec ISMAIL Livraisons

**Implémentation détaillée du système de suivi des livraisons**:

```ts
// src/services/api/servicesApi.ts
import { fetch } from 'whatwg-fetch';

export const servicesApi = {
  // ... autres méthodes
  
  trackDelivery: async (trackingId: string) => {
    const response = await fetch(`/api/deliveries/${trackingId}`);
    return await response.json();
  },
  
  updateDeliveryStatus: async (trackingId: string, status: string, location?: any) => {
    const response = await fetch(`/api/deliveries/${trackingId}/status`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        status,
        location
      })
    });
    
    return await response.json();
  }
};

// Dans le composant ServiceDetails
import { servicesApi } from '../../services/api/servicesApi';
import { deliveryApi } from '../../services/api/deliveryApi';

export const ServiceDetails: React.FC = () => {
  const [service, setService] = useState<any>(null);
  const [delivery, setDelivery] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  
  useEffect(() => {
    const serviceId = new URLSearchParams(window.location.search).get('id');
    
    if (!serviceId) {
      setError('ID de service non fourni');
      setLoading(false);
      return;
    }
    
    const fetchService = async () => {
      try {
        setLoading(true);
        const serviceData = await servicesApi.getService(serviceId);
        setService(serviceData);
        
        // Vérifier si une livraison existe pour ce service
        if (serviceData.delivery_id) {
          const deliveryData = await deliveryApi.getDelivery(serviceData.delivery_id);
          setDelivery(deliveryData);
        }
        
        setError('');
      } catch (error) {
        console.error('Erreur lors du chargement du service:', error);
        setError('Impossible de charger les détails du service');
      } finally {
        setLoading(false);
      }
    };
    
    fetchService();
  }, []);
  
  // Mise à jour automatique des statuts de livraison
  useEffect(() => {
    if (!delivery) return;
    
    const interval = setInterval(async () => {
      try {
        const updatedDelivery = await deliveryApi.getDelivery(delivery.id);
        setDelivery(updatedDelivery);
      } catch (error) {
        console.error('Erreur lors de la mise à jour du suivi:', error);
      }
    }, 30000); // Mise à jour toutes les 30 secondes
    
    return () => clearInterval(interval);
  }, [delivery]);
  
  // Affichage des étapes de livraison
  const getDeliveryStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return 'pending';
      case 'assigned':
        return 'assigned';
      case 'in_transit':
        return 'in_transit';
      case 'delivered':
        return 'delivered';
      case 'canceled':
        return 'canceled';
      default:
        return 'pending';
    }
  };
  
  return (
    <div className="service-details">
      <h2>Détails du service #{service?.id}</h2>
      
      {loading ? (
        <Loader />
      ) : error ? (
        <div className="error-message">{error}</div>
      ) : (
        <>
          <div className="service-summary">
            <h3>Résumé du service</h3>
            <div className="summary-item">
              <span>Date de création:</span>
              <span>{formatDate(service.created_at)}</span>
            </div>
            <div className="summary-item">
              <span>Statut:</span>
              <span className={`service-status ${service.status}`}>{service.status}</span>
            </div>
            <div className="summary-item">
              <span>Description:</span>
              <span>{service.description}</span>
            </div>
          </div>
          
          {delivery && (
            <div className="delivery-tracking">
              <h3>Suivi de la livraison</h3>
              
              <div className="tracking-steps">
                {[
                  { status: 'pending', label: 'Commande passée' },
                  { status: 'assigned', label: 'Livreur assigné' },
                  { status: 'in_transit', label: 'En route' },
                  { status: 'delivered', label: 'Livré' },
                  { status: 'canceled', label: 'Annulé' }
                ].map((step, index) => (
                  <div 
                    key={index}
                    className={`tracking-step ${delivery.status === step.status ? 'active' : ''}`}
                  >
                    <div className={`status-icon ${getDeliveryStatusIcon(step.status)}`}></div>
                    <div className="step-label">{step.label}</div>
                  </div>
                ))}
              </div>
              
              <div className="tracking-details">
                <div className="detail-item">
                  <span>Statut:</span>
                  <span>{getDeliveryStatusLabel(delivery.status)}</span>
                </div>
                
                <div className="detail-item">
                  <span>Livreur:</span>
                  <span>{delivery.driver.name}</span>
                </div>
                
                <div className="detail-item">
                  <span>Numéro de téléphone:</span>
                  <span>{delivery.driver.phone}</span>
                </div>
                
                <div className="detail-item">
                  <span>Heure estimée de livraison:</span>
                  <span>{delivery.estimated_delivery_time}</span>
                </div>
                
                {delivery.location && (
                  <div className="detail-item">
                    <span>Localisation actuelle:</span>
                    <span>{delivery.location.address}</span>
                  </div>
                )}
                
                <div className="detail-item">
                  <span>Date de création:</span>
                  <span>{formatDate(delivery.created_at)}</span>
                </div>
              </div>
              
              {delivery.status === 'in_transit' && (
                <div className="live-tracking">
                  <h3>Suivi en temps réel</h3>
                  <div className="map-container">
                    <Map 
                      latitude={delivery.location.latitude}
                      longitude={delivery.location.longitude}
                      zoom={14}
                    />
                  </div>
                </div>
              )}
            </div>
          )}
          
          <div className="service-actions">
            <Button 
              variant="primary" 
              onClick={() => console.log('Confirmer la réception')}
              disabled={delivery?.status !== 'delivered'}
            >
              Confirmer la réception
            </Button>
            <Button 
              variant="secondary" 
              onClick={() => navigate(`/reviews/${service.id}`)}
              disabled={delivery?.status !== 'delivered'}
            >
              Laisser un avis
            </Button>
          </div>
        </>
      )}
    </div>
  );
};
```

#### 2.6.2 Intégration avec ISMAIL Recouvrement

**Implémentation détaillée du système de suivi des créances**:

```ts
// src/services/api/creditRecoveryApi.ts
import { fetch } from 'whatwg-fetch';

export const creditRecoveryApi = {
  createClaim: async (data: any) => {
    const response = await fetch('/api/claims', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    
    return await response.json();
  },
  
  getClaim: async (claimId: string) => {
    const response = await fetch(`/api/claims/${claimId}`);
    return await response.json();
  },
  
  updateClaim: async (claimId: string, data: any) => {
    const response = await fetch(`/api/claims/${claimId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    
    return await response.json();
  },
  
  sendNotification: async (claimId: string, data: any) => {
    const response = await fetch(`/api/claims/${claimId}/notifications`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    
    return await response.json();
  }
};

// Dans le composant ServiceDetails
import { creditRecoveryApi } from '../../services/api/creditRecoveryApi';
import { servicesApi } from '../../services/api/servicesApi';

export const ServiceDetails: React.FC = () => {
  const [service, setService] = useState<any>(null);
  const [claim, setClaim] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  
  useEffect(() => {
    const serviceId = new URLSearchParams(window.location.search).get('id');
    
    if (!serviceId) {
      setError('ID de service non fourni');
      setLoading(false);
      return;
    }
    
    const fetchService = async () => {
      try {
        setLoading(true);
        const serviceData = await servicesApi.getService(serviceId);
        setService(serviceData);
        
        // Vérifier si une créance existe pour ce service
        if (serviceData.payment_status === 'failed' || serviceData.status === 'cancelled') {
          const claimData = await creditRecoveryApi.getClaimByService(serviceId);
          setClaim(claimData);
        }
        
        setError('');
      } catch (error) {
        console.error('Erreur lors du chargement du service:', error);
        setError('Impossible de charger les détails du service');
      } finally {
        setLoading(false);
      }
    };
    
    fetchService();
  }, []);
  
  const handleCreateClaim = async () => {
    try {
      if (!service) return;
      
      const claimData = await creditRecoveryApi.createClaim({
        debtor_id: service.user_id,
        creditor_id: service.provider_id,
        amount: service.total_amount,
        description: `Créance pour le service #${service.id}`,
        service_id: service.id
      });
      
      setClaim(claimData);
    } catch (error) {
      console.error('Erreur lors de la création de la créance:', error);
      setError('Impossible de créer la créance');
    }
  };
  
  const handleSendReminder = async () => {
    try {
      if (!claim) return;
      
      await creditRecoveryApi.sendNotification(claim.id, {
        type: 'reminder',
        content: `Rappel de paiement pour le service #${service.id}`
      });
      
      // Actualiser les informations de la créance
      const updatedClaim = await creditRecoveryApi.getClaim(claim.id);
      setClaim(updatedClaim);
    } catch (error) {
      console.error('Erreur lors de l\'envoi du rappel:', error);
      setError('Impossible d\'envoyer le rappel');
    }
  };
  
  return (
    <div className="service-details">
      <h2>Détails du service #{service?.id}</h2>
      
      {loading ? (
        <Loader />
      ) : error ? (
        <div className="error-message">{error}</div>
      ) : (
        <>
          <div className="service-summary">
            <h3>Résumé du service</h3>
            <div className="summary-item">
              <span>Date:</span>
              <span>{formatDate(service.created_at)}</span>
            </div>
            <div className="summary-item">
              <span>Montant total:</span>
              <span>{currencyFormat(service.total_amount)}</span>
            </div>
            <div className="summary-item">
              <span>Statut de paiement:</span>
              <span className={`payment-status ${service.payment_status}`}>{service.payment_status}</span>
            </div>
            <div className="summary-item">
              <span>Statut du service:</span>
              <span className={`service-status ${service.status}`}>{service.status}</span>
            </div>
          </div>
          
          {service.payment_status === 'failed' && (
            <div className="credit-recovery">
              <h3>Suivi de la créance</h3>
              
              {claim ? (
                <div className="claim-details">
                  <div className="detail-item">
                    <span>Statut de la créance:</span>
                    <span className={`claim-status ${claim.status}`}>{claim.status}</span>
                  </div>
                  <div className="detail-item">
                    <span>Date de création:</span>
                    <span>{formatDate(claim.created_at)}</span>
                  </div>
                  <div className="detail-item">
                    <span>Dernière action:</span>
                    <span>{claim.last_action}</span>
                  </div>
                  <div className="detail-item">
                    <span>Prochaine étape:</span>
                    <span>{claim.next_step}</span>
                  </div>
                  
                  <div className="claim-actions">
                    <Button 
                      variant="primary" 
                      onClick={handleSendReminder}
                      disabled={claim.status === 'completed' || claim.status === 'canceled'}
                    >
                      Envoyer un rappel
                    </Button>
                    <Button 
                      variant="secondary" 
                      onClick={() => navigate(`/claims/${claim.id}`)}
                    >
                      Voir les détails de la créance
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="claim-actions">
                  <Button 
                    variant="primary" 
                    onClick={handleCreateClaim}
                    disabled={claim !== null}
                  >
                    Créer une créance
                  </Button>
                </div>
              )}
            </div>
          )}
          
          <div className="service-actions">
            <Button 
              variant="primary" 
              onClick={() => console.log('Confirmer la réception')}
              disabled={service.status !== 'completed'}
            >
              Confirmer la réception
            </Button>
            <Button 
              variant="secondary" 
              onClick={() => navigate(`/reviews/${service.id}`)}
              disabled={service.status !== 'completed'}
            >
              Laisser un avis
            </Button>
          </div>
        </>
      )}
    </div>
  );
};
```

### 2.7 Points de Vigilance et Bonnes Pratiques

#### 2.7.1 Gestion des disponibilités et réservations

**Implémentation détaillée du système de gestion des disponibilités**:

```ts
// src/services/provider/providerService.ts
import { fetch } from 'whatwg-fetch';

export const providerService = {
  // ... autres méthodes
  
  getAvailability: async (providerId: string, date: string) => {
    const response = await fetch(`/api/providers/${providerId}/availability?date=${date}`);
    return await response.json();
  },
  
  updateAvailability: async (providerId: string, availability: any) => {
    const response = await fetch(`/api/providers/${providerId}/availability`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(availability)
    });
    
    return await response.json();
  },
  
  getWorkingHours: async (providerId: string) => {
    const response = await fetch(`/api/providers/${providerId}/working-hours`);
    return await response.json();
  },
  
  updateWorkingHours: async (providerId: string, workingHours: any) => {
    const response = await fetch(`/api/providers/${providerId}/working-hours`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(workingHours)
    });
    
    return await response.json();
  }
};

// Dans le composant ProviderProfile
import { providerService } from '../../services/provider/providerService';

export const ProviderProfile: React.FC = () => {
  const [provider, setProvider] = useState<any>(null);
  const [availability, setAvailability] = useState<any>(null);
  const [workingHours, setWorkingHours] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  
  useEffect(() => {
    const providerId = new URLSearchParams(window.location.search).get('id');
    
    if (!providerId) {
      setError('ID de fournisseur non fourni');
      setLoading(false);
      return;
    }
    
    const fetchProvider = async () => {
      try {
        setLoading(true);
        const providerData = await providerService.getProvider(providerId);
        setProvider(providerData);
        
        // Charger les disponibilités pour la date actuelle
        const today = new Date().toISOString().split('T')[0];
        const availabilityData = await providerService.getAvailability(providerId, today);
        setAvailability(availabilityData);
        
        // Charger les horaires de travail
        const workingHoursData = await providerService.getWorkingHours(providerId);
        setWorkingHours(workingHoursData);
        
        setError('');
      } catch (error) {
        console.error('Erreur lors du chargement du fournisseur:', error);
        setError('Impossible de charger les détails du fournisseur');
      } finally {
        setLoading(false);
      }
    };
    
    fetchProvider();
  }, []);
  
  const handleAvailabilityChange = async (date: string, status: 'available' | 'unavailable') => {
    try {
      setLoading(true);
      const updatedAvailability = await providerService.updateAvailability(provider.id, {
        date,
        status
      });
      setAvailability(updatedAvailability);
      setError('');
    } catch (error) {
      console.error('Erreur lors de la mise à jour des disponibilités:', error);
      setError('Impossible de mettre à jour les disponibilités');
    } finally {
      setLoading(false);
    }
  };
  
  const handleWorkingHoursChange = async (day: string, open: string, close: string) => {
    try {
      setLoading(true);
      const updatedWorkingHours = await providerService.updateWorkingHours(provider.id, {
        day,
        open,
        close
      });
      setWorkingHours(updatedWorkingHours);
      setError('');
    } catch (error) {
      console.error('Erreur lors de la mise à jour des horaires de travail:', error);
      setError('Impossible de mettre à jour les horaires de travail');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="provider-profile">
      <h2>Profil du fournisseur</h2>
      
      {loading ? (
        <Loader />
      ) : error ? (
        <div className="error-message">{error}</div>
      ) : (
        <>
          <div className="provider-info">
            <h3>Informations de base</h3>
            <div className="info-item">
              <span>Nom de l'entreprise:</span>
              <span>{provider.business_name}</span>
            </div>
            <div className="info-item">
              <span>Catégorie:</span>
              <span>{provider.category}</span>
            </div>
            <div className="info-item">
              <span>Description:</span>
              <span>{provider.description}</span>
            </div>
          </div>
          
          <div className="working-hours">
            <h3>Horaire de travail</h3>
            <div className="hours-list">
              {workingHours.map((day: any) => (
                <div key={day.day} className="hour-item">
                  <div className="day">{day.day}</div>
                  <div className="time">
                    {day.open === 'closed' ? 'Fermé' : `${day.open} - ${day.close}`}
                  </div>
                  <div className="actions">
                    <Button 
                      variant="secondary" 
                      onClick={() => handleWorkingHoursChange(day.day, 'closed', 'closed')}
                    >
                      Fermer
                    </Button>
                    <Button 
                      variant="secondary" 
                      onClick={() => handleWorkingHoursChange(day.day, '09:00', '17:00')}
                    >
                      9h-17h
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          <div className="availability">
            <h3>Disponibilités</h3>
            <div className="calendar">
              {availability.map((day: any) => (
                <div key={day.date

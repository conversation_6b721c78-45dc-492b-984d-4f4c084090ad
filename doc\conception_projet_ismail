# **Documentation Complète et Détaillée de Conception du Projet d'Application ISMAIL**  
*Conforme au Guide Technique Détaillé pour la Stack Technique Standardisée*  

---

## **1. Introduction & Overview**

### **1.1 Contexte Stratégique et Vision**

ISMAIL est une super-plateforme digitale panafricaine conçue pour révolutionner l'interaction entre professionnels et clients à travers un écosystème unique, intégré et 100% digital. Ce projet répond aux défis structurels des PME africaines en offrant une infrastructure technologique adaptée à leur contexte spécifique.

**Stratégie de Noms de Domaine** :  
ISMAIL exploite deux noms de domaine complémentaires pour optimiser sa présence digitale et sa stratégie de marché :  
- **http://myismail.com/** : Domaine principal utilisé pour le marché francophone et les pays anglophones avec une forte présence francophone  
- **https://ismail.africa/** : Domaine secondaire utilisé pour le marché africain général, avec une focalisation sur les pays anglophones et l'identité continentale  

**Stratégie de redirection et de gestion des noms de domaine** :  
- Configuration DNS :  
  - `myismail.com` : Pointe vers les serveurs principaux de Netlify avec un enregistrement A et un enregistrement CNAME  
  - `ismail.africa` : Pointe vers les mêmes serveurs de Netlify avec un enregistrement A et un enregistrement CNAME  
- Configuration de Netlify :  
  - Les deux domaines sont ajoutés dans la section "Custom domains" de Netlify  
  - Redirections automatiques configurées pour le trafic francophone vers `myismail.com` et anglophone vers `ismail.africa`  
  - SSL/TLS automatique pour les deux domaines via Let's Encrypt  
- Configuration de Netlify pour les deux domaines :  
  ```toml
  # netlify.toml
  [[redirects]]
    from = "https://www.myismail.com/*"
    to = "https://myismail.com/:splat"
    status = 301
    force = true

  [[redirects]]
    from = "https://www.ismail.africa/*"
    to = "https://ismail.africa/:splat"
    status = 301
    force = true

  [[redirects]]
    from = "https://myismail.com/*"
    to = "https://ismail.africa/:splat"
    status = 302
    force = true
    conditions = {Country = ["FR", "BE", "CA", "CH", "SN", "CI", "CM", "BJ", "TG", "MA", "TN", "DZ", "MG", "ML", "BF", "NE", "CD", "CG", "GA", "CI", "GW", "LR", "SL", "SC", "ST", "TG", "BJ", "CM", "CF", "GQ", "GA", "CD", "CG", "TD", "DJ", "ER", "KM", "MG", "MR", "MU", "RW", "SC", "SD", "SO", "SS", "SY", "TZ", "UG", "ZM", "ZW"]}

  [[redirects]]
    from = "https://ismail.africa/*"
    to = "https://myismail.com/:splat"
    status = 302
    force = true
    conditions = {Country = ["US", "GB", "IN", "KE", "NG", "GH", "ZA", "TZ", "UG", "RW", "LS", "BW", "NA", "MZ", "ZM", "ZW", "SD", "SO", "SS", "SY", "EG", "LY", "DZ", "MA", "TN", "JO", "IQ", "SA", "AE", "OM", "KW", "BH", "QA", "YE", "IR", "PK", "BD", "LK", "MM", "TH", "VN", "ID", "PH", "MY", "SG", "HK", "TW", "KR", "JP", "CN", "AU", "NZ", "CA", "US", "GB", "DE", "FR", "ES", "IT", "NL", "BE", "SE", "DK", "NO", "FI", "CH", "AT", "PT", "GR", "PL", "CZ", "HU", "RO", "BG", "HR", "SI", "SK", "LT", "LV", "EE", "MT", "CY", "IS", "MC", "SM", "VA", "AD", "LI", "JE", "GG", "IM"]}
  ```

**Impact Stratégique des Deux Noms de Domaine** :  
- **myismail.com** : Cible les pays francophones d'Afrique et d'ailleurs avec une interface en français, portugais et arabe  
- **ismail.africa** : Cible les pays anglophones d'Afrique avec une interface en anglais et swahili  
- **Avantages** :  
  - Meilleure référencement localisé pour chaque marché  
  - Meilleure expérience utilisateur avec des contenus adaptés à la langue locale  
  - Meilleure gestion des cookies et des sessions selon la localisation géographique  
  - Meilleure conformité aux réglementations locales de chaque pays  
  - Meilleure gestion des données selon les réglementations locales  

### **1.2 Ambition et Objectifs**

**Objectifs Stratégiques** :  
- Devenir la plateforme de référence pour les PME en Afrique d'ici 5 ans  
- Couvrir 80% des pays africains avec des adaptations locales  
- Atteindre 1 million de professionnels actifs sur la plateforme en 3 ans  
- Générer 500 000 emplois indirects grâce à la croissance des PME utilisatrices  
- Réduire le coût de la digitalisation pour les PME de 70% par rapport aux solutions existantes  

**Objectifs Opérationnels (Années 1-3)** :  
- **Année 1** : Déploiement dans 5 pays pilotes (Sénégal, Côte d'Ivoire, Cameroun, Bénin, Togo) avec 50 000 professionnels actifs  
- **Année 2** : Extension à 15 pays avec 200 000 professionnels actifs et 5 modules complémentaires  
- **Année 3** : Couverture de 25 pays avec 500 000 professionnels actifs et intégration de l'IA avancée  

**Indicateurs Clés de Succès (KPI)** :  
- Taux d'adoption par les professionnels : > 65% des professionnels ciblés dans chaque zone géographique  
- Taux de rétention à 6 mois : > 80%  
- Temps moyen de mise en ligne d'un professionnel : < 24h  
- Taux de satisfaction client : > 90% (mesuré trimestriellement)  
- Consommation moyenne de crédits par professionnel actif : > 1 500 crédits/mois  

### **1.3 Cadre de Référence et Normes de Qualité**

**Normes de Qualité Logicielle** :  
- **ISO/IEC 25010:2011** (Qualité des systèmes et logiciels)  
- **CMMI Niveau 3** (Capacité Maturité Modèle Intégration)  
- **ISO 9001:2015** (Système de management de la qualité)  
- **ISO/IEC 33000:2015** (Évaluation de la qualité des processus)  

**Normes de Sécurité** :  
- **ISO/IEC 27001:2013** (Système de management de la sécurité de l'information)  
- **PCI DSS v3.2.1** (Pour les transactions de paiement)  
- **GDPR** (Adapté aux réglementations locales africaines)  
- **NIST Cybersecurity Framework**  

**Normes Spécifiques au Contexte Africain** :  
- **Code de conduite de la CEDEAO** sur les services numériques  
- **Réglementations nationales des pays d'implémentation** (ex: RGPD Sénégalais)  
- **Normes de l'Union Africaine** sur l'économie numérique (2020-2030)  

**Processus Qualité Appliqués** :  
- Revue de code obligatoire pour chaque commit  
- Test de qualité continue (TQC) avec seuil minimal de 85%  
- Cycle de développement agile avec sprints de 2 semaines  
- Documentation complète de chaque fonctionnalité  
- Audit de sécurité trimestriel par un tiers indépendant  

---

## **2. Architecture Générale**

### **2.1 Présentation de l'Architecture Modulaire**

ISMAIL repose sur une architecture modulaire favorisant l'évolutivité, conçue pour s'adapter aux spécificités du marché africain. L'architecture se compose de deux couches principales :  
1. **ISMAIL Core Platform**: Cœur central fournissant des services transverses  
2. **Modules Métiers**: Onze modules spécialisés interconnectés mais indépendants  

**Principes Architecturaux Fondamentaux** :  
- **Loose coupling**: Les modules sont faiblement couplés pour permettre des évolutions indépendantes  
- **High cohesion**: Chaque module est fortement cohésif autour de ses fonctionnalités métier  
- **API-first**: Toutes les interactions se font par le biais d'APIs bien documentées  
- **Event-driven**: Communication asynchrone via un bus d'événements pour la résilience  
- **Progressive enhancement**: Expérience optimisée pour les terminaux bas de gamme avec amélioration progressive  

**Schéma d'Architecture Global (détail technique)** :  

```
+---------------------------------------------------------------------+
| APPLICATION CLIENT (PWA)                                            |
| +----------------+ +----------------+ +--------------+                |
| | React Frontend |<----->| Service Workers|<----->| Cache API  |                |
| +----------------+ +----------------+ +--------------+                |
+---------------------------------------------------------------------+ ▲
| HTTP/2+ TLS 1.3                                                    ▼
+---------------------------------------------------------------------+
| API GATEWAY (Netlify)                                               |
| +----------------+ +----------------+ +--------------+              |
| | Rate Limiting  | | Authentication | | Request    |              |
| | & Throttling   | | & Authorization| | Validation |              |
| +----------------+ +----------------+ +--------------+              |
+---------------------------------------------------------------------+ ▲
| API calls (REST/GraphQL)                                           ▼
+---------------------------------------------------------------------+
| ISMAIL CORE PLATFORM                                                |
| +--------------+ +--------------+ +--------------+ +--------------+ |
| | Auth0        | | Neon DB      | | Payment    | | Document     | |
| | Integration  | | (PostgreSQL) | | Gateway    | | Management   | |
| +--------------+ +--------------+ +--------------+ +--------------+ |
| +--------------+ +--------------+ +--------------+ +--------------+ |
| | KYC Service  | | Notification | | Credit     | | Geolocation  | |
| | (Biométrie)  | | Service      | | Management | | Service      | |
| +--------------+ +--------------+ +--------------+ +--------------+ |
+---------------------------------------------------------------------+ ▲
| Event Bus (Kafka)                                                  ▼
+---------------------------------------------------------------------+
| BUSINESS MODULES                                                    |
| +--------------+ +--------------+ +--------------+ +--------------+ |
| | ISMAIL Shop  | | ISMAIL       | | ISMAIL       | | ISMAIL       | |
| | (E-commerce) | | Services     | | Booking      | | Immobilier   | |
| +--------------+ +--------------+ +--------------+ +--------------+ |
| +--------------+ +--------------+ +--------------+ +--------------+ |
| | ISMAIL       | | ISMAIL       | | ISMAIL       | | ISMAIL       | |
| | Recouvrement | | Livraisons   | | Location     | | VTC          | |
| +--------------+ +--------------+ +--------------+ +--------------+ |
| +--------------+ +--------------+ +--------------+                |
| | ISMAIL       | | ISMAIL       | | ISMAIL       |                |
| | Education    | | Santé        | | Téléphonie   |                |
| +--------------+ +--------------+ +--------------+                |
+---------------------------------------------------------------------+
```

**Détail des Composants de l'API Gateway (Netlify)** :  
- **Endpoint de base** :  
  - `https://api.myismail.com/v1/` pour le domaine francophone  
  - `https://api.ismail.africa/v1/` pour le domaine anglophone  
- **Gestion des versions** : Versioning via header "X-API-Version" ou dans l'URL (v1, v2, etc.)  
- **Rate Limiting** : 50 requêtes/minute par utilisateur, extensible via configuration  
- **Caching** : Stratégie de cache en plusieurs niveaux (Edge, CDN, App)  
- **Sécurité** : Jetons JWT validés, protection contre les attaques DDoS  
- **Monitoring** : Métriques en temps réel avec alertes automatiques  
- **Documentation** : API documentation générée automatiquement (OpenAPI/Swagger)  

**Caractéristiques Spécifiques de l'Architecture pour le Contexte Africain** :  
- **Optimisation pour les réseaux mobiles africains** : Compression des payloads à 90% (Brotli), taille maximale de requête < 50KB  
- **Architecture mobile-first avec PWA** : Capacité à fonctionner hors ligne avec synchronisation intelligente  
- **Adaptation aux terminaux bas de gamme** : Support des appareils avec < 1GB de RAM  
- **Gestion de la connectivité intermittente** : Mécanismes de retry avec backoff exponentiel  
- **Optimisation de la consommation de données** : Minimisation des échanges réseau  

### **2.2 Stack Technique**

**Infrastructure et Développement** :  
- **Gestion du Code Source** : GitHub (dépôts privés/publiques)  
  - 3 environnements : Development, Staging, Production  
  - Workflow : Feature branches → PR → Review → Merge → CI/CD  
  - Protection des branches principales : Reviews obligatoires, tests passés  
  - Intégration continue : GitHub Actions avec pipeline de validation  

- **Base de Données** : Neon (PostgreSQL Serverless)  
  - Architecture : Primary (us-east-1) + 2 Read Replicas (eu-west-1, af-south-1)  
  - Performance : 4 vCPU, 4GB RAM, 50GB stockage par instance  
  - Sécurité : Chiffrement AES-256 à l'arrêt et en transit  
  - Backups : Quotidiens avec rétention de 30 jours  
  - Monitoring : Métriques en temps réel avec alertes de seuil  
  - Scaling : Auto-scaling selon charge (de 1 à 16 vCPU)  

- **Authentification** : Auth0  
  - Configuration : Tenant dédié avec domaine personnalisé (auth.myismail.com et auth.ismail.africa)  
  - Méthodes d'authentification : MFA obligatoire pour les comptes professionnels  
  - RBAC : Rôles personnalisés avec permissions granulaires  
  - SSO : Configuration avec les principaux fournisseurs locaux  
  - Limites de sécurité : 5 tentatives de connexion max, verrouillage temporaire  
  - Journalisation : Toutes les actions authentifiées sont enregistrées  

- **Hébergement Frontend** : Netlify  
  - Configuration : Build automatisé à chaque push sur main  
  - Performance : CDN mondial avec points de présence en Afrique (Dakar, Abidjan, Lagos)  
  - PWA : Service workers, manifest, icônes pour installation  
  - Analytics : Intégration avec Google Analytics et Matomo  
  - A/B Testing : Configuration pour tests utilisateur  
  - Formes de déploiement : Canary, Blue/Green, Progressive  

- **Application Frontend** : React.js avec TypeScript  
  - Version : React 18.2.0  
  - State Management : Redux Toolkit  
  - Routing : React Router v6  
  - Formulaires : Formik + Yup  
  - Style : CSS Modules avec design system personnalisé  
  - Tests : Jest, React Testing Library, Cypress  

- **UI/UX** : Charte graphique ISMAIL intégrant turquoise (#40E0D0) et orange (#f27120)  
  - Design System : Composants réutilisables documentés  
  - Responsivité : Mobile-first avec breakpoints spécifiques  
  - Accessibilité : Conformité WCAG 2.1 niveau AA  
  - Internationalisation : Support de 5 langues (fr, en, pt, ar, locales)  
  - Performance : Score Lighthouse > 90  

**Infrastructure Cloud** :  
- **Cloud Provider** : AWS avec régions spécifiques pour l'Afrique  
- **Régions** :  
  - us-east-1 (Virginie) : Services principaux  
  - eu-west-1 (Irlande) : Backups et réplication  
  - af-south-1 (Cape Town) : Points de présence pour l'Afrique australe  
- **Services Critiques** :  
  - VPC : Isolation réseau avec sous-réseaux publics/privés  
  - Load Balancing : ALB avec WAF intégré  
  - Container Orchestration : EKS (Kubernetes)  
  - Monitoring : CloudWatch + Prometheus/Grafana  
  - Logs : CloudWatch Logs + ELK Stack  

**Orchestration et CI/CD** :  
- **CI/CD** : GitHub Actions avec workflow détaillé  
  - Étapes : Build → Tests unitaires → Tests d'intégration → Déploiement staging → Tests E2E → Déploiement production  
  - Durée cible : < 15 minutes pour le pipeline complet  
  - Sécurité : Scanning de vulnérabilités intégré  
  - Rollback : Automatique en cas d'échec de déploiement  

- **Orchestration** : Kubernetes (EKS)  
  - Cluster size : 12 nodes (m5.xlarge) en production  
  - Auto-scaling : Basé sur CPU, mémoire et requêtes/secondes  
  - Monitoring : Prometheus + Grafana  
  - Logging : Fluentd + ELK Stack  

**Infrastructure de Sécurité** :  
- **Chiffrement** : TLS 1.3 pour toutes les communications  
- **Secrets management** : HashiCorp Vault  
- **Network security** : Security groups stricts, NACLs  
- **Compliance** : Audit trimestriel par tiers indépendant  
- **Incident response** : Plan documenté avec simulation mensuelle  

---

## **3. Charte Graphique**

### **3.1 Palette de Couleurs Officielles**

**Couleurs Primaires et Secondaires** :  

| Nom           | Code Hexa | Code RGB      | Usage Principal                                  | Signification                                                                 |
|---------------|-----------|---------------|--------------------------------------------------|-------------------------------------------------------------------------------|
| Turquoise     | #40E0D0   | RGB(64, 224, 208) | Éléments de navigation principaux, boutons CTA, titres | Évoque la pureté, la simplicité et la clarté, permettant une lecture facile du contenu |
| Orange        | #f27120   | RGB(242, 113, 32) | Boutons d'action critique, appels à l'action, éléments de notification | Évoque l'énergie, la créativité et l'engagement                                |
| Blanc         | #FFFFFF   | RGB(255, 255, 255) | Fond principal, zones de contenu                 | Pureté, espace et lisibilité                                                   |
| Noir          | #000000   | RGB(0, 0, 0)    | Texte principal, bordures, éléments de structure | Rigoureux et professionnel, garantissant un contraste optimal                  |

**Ratio Chromatique Recommandé** :  
- 60% blanc : Fond principal des écrans  
- 30% turquoise : Éléments principaux de l'interface  
- 8% orange : Éléments d'action et de notification  
- 2% noir : Texte et éléments de structure  

**Détail des Applications Colorimétriques** :  

1. **Interface Utilisateur Standard** :  
   - Fond principal : Blanc (#FFFFFF)  
   - Barre de navigation supérieure : Turquoise (#40E0D0)  
   - Barre latérale (si présente) : Turquoise (#40E0D0)  
   - Boutons principaux : Orange (#f27120) avec texte blanc  
   - Boutons secondaires : Turquoise (#40E0D0) avec contour noir  
   - Texte principal : Noir (#000000)  
   - Texte secondaire : Gris moyen (#666666)  
   - Éléments de progression : Turquoise (#40E0D0)  

2. **États et Feedback** :  
   - Succès : Vert (#4CAF50)  
   - Information : Bleu (#2196F3)  
   - Attention : Jaune (#FFC107)  
   - Erreur : Rouge (#F44336)  
   - Chargement : Turquoise (#40E0D0) avec progression orange (#f27120)  

3. **Éléments de Navigation** :  
   - Élément actif : Turquoise (#40E0D0) avec texte blanc  
   - Élément inactif : Gris moyen (#666666)  
   - Élément survolé : Turquoise très clair (#E0F7FA)  

**Contrastes Minimums** :  
- Texte standard sur fond clair : Contraste de 15.8:1 (bien au-dessus du seuil WCAG 2.1 de 4.5:1)  
- Texte secondaire : Contraste de 11:1 (supérieur au seuil de 3:1)  
- Boutons CTA : Contraste de 4.7:1 entre le texte blanc et l'orange (respectant le seuil de 4.5:1)  
- Texte sur fond turquoise : Contraste de 4.9:1 (respectant le seuil de 4.5:1)  

**Spécifications de Couleurs pour les Modules** :  
- **ISMAIL Shop** : Turquoise pour les boutons d'achat, orange pour les promotions  
- **ISMAIL Services** : Turquoise pour les devis, orange pour les demandes urgentes  
- **ISMAIL Booking** : Turquoise pour les réservations confirmées, orange pour les réservations en attente  
- **ISMAIL Immobilier** : Turquoise pour les visites confirmées, orange pour les visites en attente  
- **ISMAIL Recouvrement** : Turquoise pour les paiements confirmés, orange pour les relances  
- **ISMAIL Livraisons** : Turquoise pour les livraisons en cours, orange pour les livraisons en retard  
- **ISMAIL Location** : Turquoise pour les réservations confirmées, orange pour les options de boost  
- **ISMAIL VTC** : Turquoise pour les courses confirmées, orange pour les courses en attente  
- **ISMAIL Education** : Turquoise pour les cours disponibles, orange pour les inscriptions en attente  
- **ISMAIL Santé** : Turquoise pour les rendez-vous confirmés, orange pour les rendez-vous en attente  
- **ISMAIL Téléphonie** : Turquoise pour les produits disponibles, orange pour les offres spéciales  

### **3.2 Règles d'Application des Couleurs**

**Hiérarchie Visuelle** :  
- **Niveau 1 (éléments critiques)** : Utiliser l'orange (#f27120) pour les actions qui nécessitent une réponse immédiate de l'utilisateur  
- **Niveau 2 (éléments principaux)** : Utiliser le turquoise (#40E0D0) pour les éléments qui font partie du flux de navigation principal  
- **Niveau 3 (éléments secondaires)** : Utiliser des nuances de gris pour les éléments non critiques  
- **Niveau 4 (texte)** : Utiliser le noir (#000000) pour le texte principal, du gris moyen (#666666) pour le texte secondaire  

**Exemples d'Application** :  

1. **Boutons CTA** :  
   - Bouton "Valider" dans un formulaire : Arrière-plan orange (#f27120), texte blanc  
   - Bouton "Annuler" : Arrière-plan blanc, contour turquoise (#40E0D0), texte noir  

2. **Notifications** :  
   - Notification de succès : Fond turquoise (#E0F7FA), icône turquoise (#40E0D0)  
   - Notification d'erreur : Fond orange très clair (#FFEBEE), icône orange (#f27120)  

3. **Éléments de Navigation** :  
   - Élément actif : Turquoise (#40E0D0) avec texte blanc  
   - Élément inactif : Gris moyen (#666666) avec texte noir  
   - Élément survolé : Turquoise très clair (#E0F7FA) avec texte noir  

**Interdictions Absolues** :  
- Jamais utiliser du texte blanc sur fond turquoise ou orange : Problèmes de lisibilité et contraste insuffisant  
- Ne pas modifier les valeurs hexadécimales des couleurs officielles : Garantir la cohérence de la marque  
- Éviter d'utiliser plus de trois polices différentes dans une même interface : Maintenir la cohérence visuelle  
- Ne pas utiliser de couleurs complémentaires non autorisées : Respecter strictement la palette définie  
- Ne pas dépasser le ratio chromatique défini : Respecter 60% blanc, 30% turquoise, 8% orange, 2% noir  

**Vérification des Contrastes** :  
- Outils recommandés : WebAIM Contrast Checker, Color Oracle  
- Processus : Vérification systématique avant validation de chaque interface  
- Documentation : Rapport de conformité fourni pour chaque écran  

### **3.3 Typographie**

**Police Principale** :  
- **Nom** : Roboto (Google Fonts)  
- **Raisons du choix** :  
  - Lisibilité optimale sur écran, même sur les terminaux bas de gamme  
  - Support multilingue complet (incluant les langues africaines)  
  - Modernité et professionnalisme  
  - Compatibilité avec les appareils mobiles  
  - Poids léger (20KB pour les 4 variantes principales)  
- **Utilisation** : Corps de texte, formulaires, contenus principaux  
- **Alignement** : Toujours justifié à gauche pour les langues latines  
- **Couleur du texte** : Noir (#000000) pour tous les textes principaux, gris foncé (#333333) pour les textes secondaires  

**Hiérarchie Typographique** :  

| Élément            | Taille | Poids       | Usage                                 | Ligne de base |
|--------------------|--------|-------------|---------------------------------------|--------------|
| Titre principal    | 24 px  | Bold (700)  | Pages principales, sections majeures  | 1.3          |
| Sous-titre         | 20 px  | Semi-bold (600) | Sous-sections, titres de modules    | 1.3          |
| Titre de section   | 18 px  | Medium (500) | Titres de contenus, annonces          | 1.3          |
| Texte principal    | 16 px  | Regular (400) | Corps de texte, descriptions          | 1.5          |
| Texte secondaire   | 14 px  | Light (300)  | Légendes, détails, informations complémentaires | 1.4 |
| Micro-texte        | 12 px  | Extra-light (200) | Mentions légales, notes             | 1.3          |

**Règles d'Application** :  
- **Interligne** : 1.5 pour le corps de texte, 1.3 pour les titres  
- **Tracking (espacement des caractères)** : 0.5 pour les titres, 0 pour le corps  
- **Ligne de base** : Maintenue constante pour une lecture fluide  
- **Longueur de ligne** : 50-75 caractères pour une lecture optimale  
- **Espacement vertical** : 1.5x la taille de police entre paragraphes  

**Spécifications pour les Modules** :  
- **ISMAIL Shop** : Titres produits en 18px, descriptions en 14px  
- **ISMAIL Services** : Descriptions en 14px, détails techniques en 12px  
- **ISMAIL Booking** : Informations de réservation en 16px, détails en 14px  
- **ISMAIL Immobilier** : Descriptions en 14px, détails techniques en 12px  
- **ISMAIL Recouvrement** : Informations financières en 16px, détails en 14px  
- **ISMAIL Livraisons** : Informations de livraison en 16px, détails en 14px  
- **ISMAIL Location** : Informations de réservation en 16px, détails en 14px  
- **ISMAIL VTC** : Informations de course en 16px, détails en 14px  
- **ISMAIL Education** : Titres de cours en 18px, descriptions en 14px  
- **ISMAIL Santé** : Informations médicales en 16px, détails en 14px  
- **ISMAIL Téléphonie** : Titres de produits en 18px, descriptions en 14px  

### **3.4 Éléments d'Interface**

**Boutons** :  
- **Boutons Principaux (CTA)** :  
  - Couleur : Orange (#f27120)  
  - Forme : Bordures arrondies (4px)  
  - Effet hover : Assombrissement de 10% de la couleur  
  - Effet actif : Assombrissement de 20% + légère réduction de taille (2%)  
  - Padding : 12px 24px  
  - Ombre : Légère ombre portée (0 2px 4px rgba(0,0,0,0.1))  
  - Texte : Blanc (#FFFFFF)  
  - Hauteur minimale : 40px  
  - Comportement : Désactivé pendant le traitement  

- **Boutons Secondaires** :  
  - Couleur : Turquoise (#40E0D0) avec contour noir  
  - Forme : Bordures arrondies (4px)  
  - Effet hover : Assombrissement de 10%  
  - Texte : Noir (#000000)  
  - Padding : 12px 24px  
  - Ombre : Aucune  
  - Hauteur minimale : 40px  

- **Boutons de Navigation** :  
  - Couleur : Transparent  
  - Texte : Noir (#000000)  
  - Effet hover : Fond turquoise très clair (#E0F7FA)  
  - Indicateur actif : Ligne inférieure orange (2px)  
  - Padding : 11px 23px  
  - Hauteur minimale : 40px  

**Cartes** :  
- Fond : Blanc (#FFFFFF)  
- Ombre : 0 2px 8px rgba(0,0,0,0.08)  
- Bordure : Aucune  
- Bordures arrondies : 6px  
- Padding : 16px  
- En-tête de carte : Bordure inférieure 1px #EEEEEE, padding inférieur 12px  
- Pied de carte : Bordure supérieure 1px #EEEEEE, padding supérieur 12px  
- Comportement : Effet hover léger (élévation de 2px)  

**Formulaires** :  
- **Champs de saisie** :  
  - Bordure : 1px #CCCCCC en état normal, 1px #40E0D0 au focus  
  - Padding : 10px 12px  
  - Hauteur : 40px  
  - Ombre au focus : 0 0 0 2px rgba(64, 224, 208, 0.2)  

- **Étiquettes** :  
  - Position : Au-dessus du champ  
  - Couleur : #666666  
  - Poids : Semi-bold (600)  

- **Messages d'erreur** :  
  - Couleur : Rouge spécifique (#E53935)  
  - Icône : Présente à gauche du message  
  - Position : En dessous du champ  
  - Poids : Regular (400)  

**Icônes** :  
- Style : Lignes épurées, contours simples  
- Taille standard : 24x24px  
- Couleur : Noir (#000000) pour les icônes principales, gris (#666666) pour les secondaires  
- Bibliothèque : Material Design Icons avec personnalisation ISMAIL  
- Animation : Micro-interactions subtiles au survol (changement de couleur ou légère rotation)  

### **3.5 Éléments Spécifiques PWA**

**Écran de Chargement** :  
- Fond blanc avec logo ISMAIL  
- Animation de chargement turquoise avec progression orange  
- Message d'attente clair et concis ("Chargement de votre expérience...")  
- Durée maximale : 3 secondes  
- Comportement : Disparaît progressivement une fois l'application chargée  
- Éléments de secours : Message en cas de chargement trop long ("Chargement en cours...")  

**Notifications Push** :  
- Fond turquoise avec texte blanc  
- Icône orange d'alerte  
- Message court et pertinent (max 120 caractères)  
- Durée d'affichage : 5 secondes  
- Comportement : Clique permettant de naviguer vers le contexte pertinent  
- Options : Désactivation possible par l'utilisateur  

**Offline Experience** :  
- Fond gris clair (#F5F5F5)  
- Icône d'avertissement orange  
- Message clair indiquant les limitations hors ligne  
- Options limitées : Consultation des données mises en cache  
- Synchronisation : Automatique dès reconnexion  
- Durée de cache : 7 jours pour les données non critiques  

**Service Worker Configuration** :  
- Stratégie de cache : Stale-while-revalidate  
- Taille maximale de cache : 100MB  
- Priorisation des ressources :  
  - Assets critiques (JS, CSS)  
  - Données utilisateur  
  - Contenu statique  
- Mécanisme de mise à jour : Vérification toutes les 24h  
- Gestion des versions : Cache par version de l'application  

---

## **4. Système Core**

### **4.1 Architecture des Composants Centraux**

Le cœur d'ISMAIL est constitué de services transverses essentiels, organisés selon une architecture microservices. Chaque service est indépendant, scalable et communiquant via des APIs RESTful et des événements asynchrones.

**Services Principaux avec Spécifications Techniques** :  

1. **Gestion de l'Identité & Authentification (Auth0)** :  
   - **Description** : Gestion des identités utilisateurs, authentification, autorisation  
   - **APIs principales** :  
     - POST /api/auth/login : Authentification des utilisateurs  
     - POST /api/auth/signup : Inscription des nouveaux utilisateurs  
     - POST /api/auth/refresh : Rafraîchissement des tokens  
     - GET /api/auth/profile : Récupération du profil utilisateur  
     - POST /api/auth/change-password : Changement de mot de passe  
   - **Spécifications** :  
     - Durée de vie des tokens : 15 minutes pour les sessions normales  
     - Rotation des tokens : Automatique après 7 minutes  
     - Stockage des tokens : HttpOnly cookies + mémoire  
     - Validation biométrique : Requise pour les transactions > 10,000 F CFA  
     - Journalisation : Toutes les authentifications sont enregistrées  

2. **KYC Biométrique** :  
   - **Description** : Vérification des identités via biométrie et documents  
   - **APIs principales** :  
     - POST /api/kyc/submit : Soumission des documents KYC  
     - POST /api/kyc/verify : Vérification biométrique  
     - GET /api/kyc/status : Statut de la vérification KYC  
     - POST /api/kyc/resubmit : Resoumission des documents  
   - **Spécifications** :  
     - Processus de vérification : < 24h  
     - Taux de réussite biométrique : > 95%  
     - Support des documents : Passeport, carte nationale, permis de conduire  
     - Stockage sécurisé : Chiffrement AES-256  
     - Conservation : 7 ans après la fin de la relation  

3. **Gestion des Rôles & Permissions (RBAC)** :  
   - **Description** : Gestion fine des accès selon les rôles utilisateur  
   - **APIs principales** :  
     - GET /api/rbac/roles : Listage des rôles  
     - POST /api/rbac/assign : Attribution de rôles  
     - GET /api/rbac/permissions : Permissions par rôle  
     - POST /api/rbac/review : Revue des permissions  
   - **Spécifications** :  
     - Hiérarchie des rôles : 5 niveaux (Admin, Gestionnaire, Commercial, Partenaire, Client)  
     - Permissions granulaires : 250+ permissions distinctes  
     - Validation en temps réel : Avant chaque action critique  
     - Journalisation : Toutes les modifications sont enregistrées  
     - Revue des permissions : Mensuelle pour les rôles sensibles  

4. **Journalisation & Audit** :  
   - **Description** : Journalisation centralisée de toutes les actions  
   - **APIs principales** :  
     - POST /api/audit/log : Enregistrement d'un événement  
     - GET /api/audit/events : Récupération des événements  
     - POST /api/audit/search : Recherche dans les journaux  
     - GET /api/audit/export : Export des journaux  
   - **Spécifications** :  
     - Format des journaux : JSON structuré  
     - Conservation : 7 ans  
     - Volume journalier : ~2TB  
     - Recherche : Temps de réponse < 2 secondes  
     - Sécurité : Chiffrement des journaux en transit et au repos  

5. **Gestion des Documents** :  
   - **Description** : Stockage et gestion des documents utilisateur  
   - **APIs principales** :  
     - POST /api/documents/upload : Upload de documents  
     - GET /api/documents/list : Listage des documents  
     - GET /api/documents/download : Téléchargement de documents  
     - POST /api/documents/delete : Suppression de documents  
   - **Spécifications** :  
     - Stockage : S3 compatible avec chiffrement AES-256  
     - Format pris en charge : PDF, JPG, PNG, DOCX, XLSX  
     - Taille maximale : 50MB par document  
     - Durée de conservation : Variable selon le type de document  
     - Versioning : Support des versions multiples  

6. **Système de Notification** :  
   - **Description** : Gestion des notifications à travers tous les canaux  
   - **APIs principales** :  
     - POST /api/notifications/send : Envoi de notification  
     - GET /api/notifications/history : Historique des notifications  
     - POST /api/notifications/preferences : Gestion des préférences  
     - POST /api/notifications/read : Marquer comme lu  
   - **Spécifications** :  
     - Canaux de notification : Push, Email, SMS, In-app  
     - Priorisation : Basée sur l'urgence et le type d'utilisateur  
     - Délai de livraison : < 2 minutes pour les notifications critiques  
     - Taux de délivrance : > 99.5%  
     - Gestion des préférences : Personnalisation fine par utilisateur  

7. **Portefeuille Électronique** :  
   - **Description** : Gestion des crédits et transactions  
   - **APIs principales** :  
     - POST /api/wallet/deposit : Dépôt de crédits  
     - POST /api/wallet/withdraw : Retrait de crédits  
     - POST /api/wallet/transfer : Transfert de crédits  
     - GET /api/wallet/balance : Solde du portefeuille  
     - POST /api/wallet/transactions : Historique des transactions  
   - **Spécifications** :  
     - Intégration Wave : API officielle de Wave  
     - Durée de transaction : < 30 secondes  
     - Journalisation : Toutes les transactions enregistrées  
     - Sécurité : Vérification double pour les transactions > 5,000 F CFA  
     - Conformité : Respect des réglementations locales  

8. **Système de Crédits** :  
   - **Description** : Gestion de l'économie de crédits ISMAIL  
   - **APIs principales** :  
     - POST /api/credits/purchase : Achat de crédits  
     - POST /api/credits/consume : Consommation de crédits  
     - GET /api/credits/balance : Solde de crédits  
     - POST /api/credits/history : Historique des crédits  
     - POST /api/credits/refund : Remboursement de crédits  
   - **Spécifications** :  
     - 1 crédit = 100 F CFA  
     - Forfaits disponibles : 500, 1,500, 5,000, 15,000 crédits  
     - Consommation : En temps réel avec réservation  
     - Historique : Conservé pendant 3 ans  
     - Audit : Journalisation complète avec signature cryptographique  

9. **Géolocalisation** :  
   - **Description** : Service de localisation pour les modules pertinents  
   - **APIs principales** :  
     - POST /api/location/update : Mise à jour de la localisation  
     - GET /api/location/history : Historique de localisation  
     - POST /api/location/verify : Vérification de localisation  
     - GET /api/location/nearby : Recherche de points proches  
   - **Spécifications** :  
     - Précision : < 20 mètres dans les zones urbaines  
     - Fréquence de mise à jour : Variable selon le contexte  
     - Conservation : 30 jours pour les données de localisation  
     - Confidentialité : Consentement explicite pour chaque usage  
     - Optimisation : Adaptation aux réseaux mobiles africains  

10. **Gestion des Transactions** :  
    - **Description** : Gestion centralisée des transactions  
    - **APIs principales** :  
      - POST /api/transactions/create : Création de transaction  
      - POST /api/transactions/confirm : Confirmation de transaction  
      - POST /api/transactions/reverse : Annulation de transaction  
      - GET /api/transactions/status : Statut de transaction  
      - POST /api/transactions/history : Historique des transactions  
    - **Spécifications** :  
      - Statuts de transaction : Pending, Completed, Failed, Reversed  
      - Durée de transaction : < 30 secondes pour les transactions simples  
      - Journalisation : Toutes les étapes enregistrées  
      - Sécurité : Validation des transactions sensibles  
      - Conformité : Respect des réglementations locales  

### **4.2 Schéma d'Intégration**

```
+----------------------+      +---------------------+      +---------------------+
| APPLICATION CLIENT   |<---->| API GATEWAY         |<---->| ISMAIL CORE         |
| (React SPA+ PWA)     |      | (Netlify)           |      | (Services transverses) |
+----------------------+      +---------------------+      +---------------------+
       ▲                          ▲                          ▲
       |                          |                          |
       |                          |                          |
       ▼                          ▼                          ▼
+----------------------+      +---------------------+      +---------------------+
| MODULES MÉTIERS      |<---->| EVENT BUS           |<---->| DATA PIPELINE       |
| (11 modules)         |      | (Kafka)             |      | (Stream Processing) |
+----------------------+      +---------------------+      +---------------------+
```

**Principes d'Architecture** :  
- **APIs standardisées** : Interfaces bien définies avec documentation OpenAPI  
- **Événements asynchrones** : Communication via un bus d'événements Kafka  
- **Données contextuelles** : Passage d'informations pertinentes entre modules  
- **Sécurité renforcée** : Toutes les communications sont chiffrées en TLS 1.3  
- **Resilience** : Mécanismes de retry avec backoff exponentiel  

**Détail des Interactions** :  

1. **Création d'une Réservation (Booking)** :  
   a. L'utilisateur crée une réservation via le module Booking  
   b. Le module Booking émet un événement "booking.created"  
   c. Le système de crédits consomme l'événement et réserve les crédits nécessaires  
   d. Le système de notifications envoie une confirmation à l'utilisateur  
   e. Si échec de réservation des crédits, annulation automatique de la réservation  

2. **Paiement via Wave** :  
   a. Le partenaire initie un achat de crédits  
   b. Le système de portefeuille redirige vers l'interface Wave  
   c. Après confirmation de paiement, Wave notifie ISMAIL  
   d. Le système de crédits crédite le compte du partenaire  
   e. Le système de notifications confirme le succès à l'utilisateur  

3. **Validation KYC** :  
   a. L'utilisateur soumet des documents KYC  
   b. Le service KYC traite les documents et émet un événement "kyc.processed"  
   c. Le système d'authentification met à jour le statut KYC  
   d. Si validation réussie, le système de notifications envoie un message de confirmation  
   e. Si échec, le système envoie un message d'erreur avec motifs  

### **4.3 Stockage des Données**

**Stockage des Documents & Médias Centralisé** :  
- **Infrastructure technique** :  
  - Système de stockage : S3 compatible (AWS S3 ou équivalent)  
  - Chiffrement : AES-256 pour les données au repos  
  - Réplication : Triplement des données dans des zones de disponibilité distinctes  
  - Durée de conservation : Variable selon le type de document (3 à 7 ans)  
  - Taille maximale : 100TB  

- **Structure de données** :  
```
documents/
  users/
    user_123/
      kyc/
        passport.pdf
        id_card.jpg
        selfie.jpg
      business/
        rc.pdf
        nif.pdf
      transactions/
        transaction_456.pdf
    user_456/
      ...
  system/
    logs/
      2023/
        01/
          01.log
    audit/
      ...
```

- **Métadonnées stockées** :  
```
{
  "document_id": "d12345",
  "user_id": "u67890",
  "document_type": "passport",
  "filename": "passport.pdf",
  "size": 1256432,
  "content_type": "application/pdf",
  "upload_date": "2023-01-15T12:30:00Z",
  "expiration_date": "2030-01-15T12:30:00Z",
  "status": "verified",
  "verification_date": "2023-01-16T08:15:00Z",
  "verification_method": "biometric",
  "hash": "sha256:abc123...",
  "tags": ["kyc", "identity"],
  "version": 1
}
```

**Spécifications de Performance** :  
- Taux de disponibilité : 99.99%  
- Latence de lecture : < 100ms  
- Capacité de stockage évolutive : Jusqu'à 100PB  
- Durée de vie des documents : Conforme aux réglementations locales  
- Temps de recherche : < 500ms pour 10M de documents  

**Journalisation & Audit** :  
- **Infrastructure technique** :  
  - Système de journalisation : ELK Stack (Elasticsearch, Logstash, Kibana)  
  - Stockage à long terme : WORM (Write Once Read Many)  
  - Indexation : Avancée avec capacités de recherche textuelle  
  - API de consultation : RESTful avec authentification  

- **Structure des journaux** :  
```
{
  "timestamp": "2023-01-15T12:30:00.123Z",
  "event_id": "e78901",
  "user_id": "u67890",
  "ip_address": "************",
  "event_type": "kyc.verification",
  "status": "success",
  "details": {
    "document_type": "passport",
    "verification_method": "biometric",
    "confidence_score": 0.98
  },
  "session_id": "s12345",
  "device_info": {
    "os": "Android",
    "os_version": "12",
    "app_version": "1.2.3"
  },
  "correlation_id": "c12345"
}
```

- **Spécifications de Sécurité** :  
  - Immuabilité : Journalisation avec signatures cryptographiques  
  - Conservation : 7 ans minimum  
  - Accès : Restreint selon le principe du moindre privilège  
  - Auditabilité : Tous les accès aux journaux sont enregistrés  
  - Chiffrement : AES-256 pour les journaux sensibles  

---

## **5. Système d'Authentification (Auth0)**

### **5.1 Architecture d'Intégration avec Auth0**

```
+-------------------------------------+
| APPLICATION CLIENT                  |
| +----------------+                  |
| | React SPA      |                  |
| | (ISMAIL App)   |                  |
| +----------------+                  |
+------------------+------------------+
|                                  |
| Authentification via Auth0       v
+------------------+------------------+
| AUTH0                             |
| +------------+ +------------+     |
| | Auth0      | | Auth0      |     |
| | Management | | Authentication|   |
| | API        | | API        |     |
| +------------+ +------------+     |
+------------------+------------------+
|                                  |
| Synchronisation avec ISMAIL Core v
+------------------+------------------+
| ISMAIL CORE PLATFORM              |
| +------------+ +------------+     |
| | User       | | KYC        |     |
| | Service    | | Service    |     |
| +------------+ +------------+     |
+------------------+------------------+
|                                  |
| Base de données                  v
+------------------+------------------+
| BASE DE DONNÉES                   |
| +------------+ +------------+     |
| | Users      | | KYC Data   |     |
| | Table      | | Table      |     |
| +------------+ +------------+     |
+-------------------------------------+
```

**Flux d'Authentification Détaillé** :  

1. **Initialisation de la session** :  
   a. L'application client initialise une session avec Auth0  
   b. Génération d'un challenge code et d'un state  
   c. Redirection vers Auth0 avec les paramètres appropriés  

2. **Authentification de l'utilisateur** :  
   a. Auth0 affiche l'interface de connexion  
   b. L'utilisateur entre ses identifiants  
   c. Vérification des identifiants par Auth0  
   d. Si MFA requis, demande de vérification supplémentaire  

3. **Validation et génération de tokens** :  
   a. Auth0 valide les informations d'identification  
   b. Génération d'un code d'autorisation  
   c. Échange du code d'autorisation contre des tokens JWT  
   d. Ajout des claims spécifiques ISMAIL  

4. **Synchronisation avec ISMAIL Core** :  
   a. Envoi des informations utilisateur à ISMAIL Core  
   b. Vérification du statut KYC  
   c. Mise à jour des informations utilisateur si nécessaire  
   d. Génération des tokens d'accès ISMAIL  

5. **Retour à l'application cliente** :  
   a. Redirection vers l'application avec les tokens  
   b. Stockage sécurisé des tokens dans l'application  
   c. Initialisation de la session utilisateur  

**Détail des Endpoints Auth0 Utilisés** :  

| Endpoint       | Méthode | Description                                     | Sécurité              |
|----------------|---------|-------------------------------------------------|-----------------------|
| /authorize     | GET     | Initie le flux d'authentification               | HTTPS                 |
| /token         | POST    | Échange un code d'autorisation contre des tokens | HTTPS + Client Secret |
| /userinfo      | GET     | Récupère les informations utilisateur           | Bearer Token          |
| /revoke        | POST    | Révoque un token                                | HTTPS + Client Secret |
| /logout        | GET     | Déconnecte l'utilisateur                        | HTTPS                 |
| /ssodata       | GET     | Récupère les données SSO                        | Bearer Token          |

### **5.2 Processus d'Inscription et de Vérification**

**Parcours Complet d'Activation de Compte (Détail)** :  

1. **Création du compte** :  
   a. Saisie des informations de base (email, téléphone, type de compte)  
   b. Vérification de l'email et du téléphone via OTP (code à usage unique)  
   c. Sélection des pays et langues de préférence  
   d. Confirmation des conditions générales et politiques de confidentialité  
   e. Génération d'un compte temporaire avec statut "pending"  

2. **Vérification KYC** :  
   a. Capture des documents d'identité (passeport, carte nationale, permis de conduire)  
   b. Capture de selfie dynamique (mouvement requis pour éviter les photos statiques)  
   c. Vérification automatisée par IA :  
      - Correspondance entre selfie et pièce d'identité (min. 95% de similarité)  
      - Validité des documents (non expirés, non altérés)  
      - Cohérence des informations fournies  
      - Absence de drapeaux rouges (sanctions, antécédents)  
   d. Vérification humaine si nécessaire (doute ou documents complexes)  
   e. Génération de la carte professionnelle digitale sécurisée  

3. **Activation du compte** :  
   a. Confirmation par email/SMS  
   b. Accès complet à la plateforme après validation KYC  
   c. Activation des fonctionnalités selon le type de compte  
   d. Génération des dashboards personnalisés  
   e. Notification de bienvenue avec guide de démarrage  

**Diagramme du Processus KYC (Détail Technique)** :  
```
Début → Saisie des informations de base → Génération compte temporaire → Vérification email(OTP) → Vérification téléphone(OTP) → Soumission KYC → Capture documents(passeport, ID) → Capture selfie dynamique → Analyse IA →[Validité?] → Oui: Génération carte pro → Non: Demande de documents supplémentaires →[Vérification humaine nécessaire?] → Oui: Vérification humaine →[Approuvé?] → Oui: Génération carte pro → Non: Rejet avec motif → Non: Génération carte pro → Confirmation compte → Activation complète
```

**Détail des Étapes KYC** :  

1. **Capture des documents** :  
   a. Format accepté : JPG, PNG, PDF  
   b. Taille max : 10MB  
   c. Résolution : Min 300 DPI  
   d. Vérification en temps réel des documents  
   e. Gestion des documents multiples (recto/verso)  

2. **Analyse IA** :  
   a. Reconnaissance optique de caractères (OCR)  
   b. Vérification de l'authenticité des documents  
   c. Comparaison des données entre documents  
   d. Analyse de la qualité des images  
   e. Détection de manipulations  

3. **Vérification biométrique** :  
   a. Capture selfie avec mouvement requis  
   b. Comparaison 3D avec les documents  
   c. Vérification liveness (détection de photos/vidéos)  
   d. Score de confiance calculé (0-1)  
   e. Seuil d'approbation : > 0.95  

4. **Validation humaine (si nécessaire)** :  
   a. Assignment à un vérificateur certifié  
   b. Vérification complète des documents  
   c. Comparaison avec les bases de données officielles  
   d. Validation des éléments douteux  
   e. Décision finale avec documentation  

### **5.3 Méthodes d'Authentification**

**Authentification Multi-Facteurs (MFA) - Détail Technique** :  

| Méthode         | Description                                      | Niveau de Sécurité | Utilisation                          | Durée de validité |
|-----------------|--------------------------------------------------|--------------------|--------------------------------------|-------------------|
| Mot de passe    | Combinaison complexe (12+ caractères)            | ★★☆☆☆              | Authentification de base             | 90 jours          |
| OTP SMS         | Code à usage unique envoyé par SMS               | ★★★☆☆              | Connexion standard                   | 5 minutes         |
| OTP Email       | Code à usage unique envoyé par email             | ★★★☆☆              | Alternative au SMS                   | 10 minutes        |
| Biométrie       | Reconnaissance faciale via selfie dynamique      | ★★★★☆              | Accès aux fonctionnalités sensibles  | Session           |
| QR Sécurisé     | Scan de QR code personnel pour vérification instantanée | ★★★★☆         | Vérification sur site des professionnels | 72h             |
| Carte Pro Digitale | Carte professionnelle avec QR intégré et données chiffrées | ★★★★☆       | Identification officielle des partenaires | Permanente      |

**Stratégie d'Authentification Adaptative (Détail)** :  

- **Facteurs de risque évalués** :  
  - Localisation géographique (pays, ville)  
  - Type d'appareil (nouveau/connu)  
  - Heure de connexion (habitudes normales)  
  - Type de transaction (sensibilité)  
  - Vitesse de navigation (comportement inhabituel)  

- **Matrice de calcul du niveau de risque** :  
  ```
  niveau_risque = (w1 × facteur1) + (w2 × facteur2) + ... + (wn × factourn)
  ```
  Où les poids (w) sont ajustés périodiquement selon les menaces détectées  

- **Seuils d'Authentification** :  
  - Si niveau_risque < 3 : Authentification standard (mot de passe + OTP)  
  - Si 3 ≤ niveau_risque < 7 : Authentification renforcée (biométrie requise)  
  - Si niveau_risque ≥ 7 : Authentification maximale (biométrie + vérification humaine)  

- **Exemples Concrets** :  
  - Connexion depuis un pays connu, même appareil, heures normales : Niveau 1  
  - Connexion depuis un pays différent, même appareil, heures normales : Niveau 3  
  - Connexion depuis un pays différent, nouvel appareil, heures normales : Niveau 5  
  - Transaction > 10,000 F CFA depuis un pays différent : Niveau 7  

### **5.4 Technologies Utilisées**

**Protocoles d'Authentification** :  
- **OAuth 2.0** :  
  - Flows utilisés : Authorization Code avec PKCE  
  - Durée de vie des tokens : 15 minutes  
  - Rotation automatique : Après 7 minutes  
  - Requêtes sécurisées : HTTPS obligatoire  
  - Validation des tokens : Par le service d'autorisation  

- **OpenID Connect** :  
  - Version : 1.0  
  - ID Tokens : Utilisés pour les informations utilisateur  
  - Configuration : Découverte dynamique  
  - Algorithme : RS256 pour la signature  
  - Claims personnalisés : Incluant le statut KYC  

**Stockage des Identifiants** :  
- **Hachage** : bcrypt avec work factor 12  
- **Salage** : Génération de sel aléatoire unique par utilisateur  
- **Stockage** : Seulement les hachages dans la base de données  
- **Comparaison** : En temps constant pour éviter les attaques chronométriques  
- **Mise à jour** : Automatique lors des changements de mot de passe  

**Gestion des Sessions** :  
- **JWT (JSON Web Tokens)** :  
  - Structure : Header (alg, typ), Payload (claims), Signature  
  - Algorithme : RS256  
  - Durée de vie : 15 minutes pour les sessions normales  
  - Stockage : HttpOnly cookies + mémoire (non accessible par JavaScript)  
  - Rotation : Automatique après 7 minutes  

- **Sécurité des Tokens** :  
  - Signature cryptographique  
  - Validation de l'audience (aud) et de l'émetteur (iss)  
  - Vérification de la date d'expiration (exp)  
  - Stockage sécurisé avec protection contre le vol  
  - Revocation possible en cas de compromission  

**Validation Biométrique** :  
- **Technologies Utilisées** :  
  - API tierce spécialisée : Liveness detection (ex: Onfido)  
  - Algorithme propriétaire ISMAIL : Comparaison 3D  
  - Machine learning : Modèles d'identification  

- **Processus de Validation** :  
  - Capture de selfie avec mouvement requis  
  - Extraction des caractéristiques biométriques  
  - Comparaison avec les documents fournis  
  - Calcul d'un score de confiance (0-1)  
  - Validation si score > seuil configuré (0.95)  

- **Sécurité** :  
  - Données biométriques : Chiffrées AES-256  
  - Stockage : Temporaire pendant le processus  
  - Conservation : Aucune donnée stockée après validation  
  - Conformité : RGPD et réglementations locales  

**Chiffrement des Communications** :  
- **TLS 1.3** :  
  - Version minimale : 1.3 (Aucun support pour TLS 1.0/1.1)  
  - Cipher suites : Chiffrements modernes (AES-256-GCM, ChaCha20-Poly1305)  
  - Perfect Forward Secrecy : Obligatoire  
  - Certificate Transparency : Activé  
  - HSTS : Max-age=31536000; includeSubDomains  

- **Chiffrement de Bout en Bout** :  
  - Pour les données sensibles (ex: KYC)  
  - Utilisation de clés asymétriques  
  - Rotation automatique des clés  
  - Stockage sécurisé des clés  
  - Validation des certificats  

**Spécifications de Sécurité Détaillées** :  
- **Durée de vie des Tokens** :  
  - Session normale : 15 minutes  
  - Transactions sensibles : 5 minutes  
  - Refresh tokens : 24h avec utilisation unique  
  - Rotation automatique : Tous les 7 minutes  

- **Rotation Automatique des Clés de Chiffrement** :  
  - Fréquence : Tous les 30 jours  
  - Mécanisme : Déploiement en rolling update  
  - Stockage : Dans HashiCorp Vault  
  - Audit : Journalisation complète des rotations  
  - Récupération : Processus de failover en cas de problème  

- **Journalisation Complète** :  
  - Événements critiques : Toutes les authentifications  
  - Format : JSON structuré avec horodatage précis  
  - Stockage : Système WORM pour immuabilité  
  - Durée de conservation : 7 ans  
  - Accessibilité : Via API sécurisée  

---

## **6. Système KYC**

### **6.1 Processus de Vérification d'Identité**

**Étapes de Vérification (Détail Opérationnel)** :  

1. **Préparation des Documents** :  
   a. Vérification de la qualité des images (luminosité, netteté)  
   b. Validation des formats acceptés (JPG, PNG, PDF)  
   c. Vérification des tailles de fichiers (5MB max)  
   d. Détection automatique des documents (passeport, carte d'identité)  
   e. Vérification de l'absence de masquage ou de manipulation  

2. **Capture des Documents** :  
   a. Interface utilisateur guide l'utilisateur dans la capture  
   b. Vérification en temps réel de la qualité des images  
   c. Aide visuelle pour le positionnement des documents  
   d. Capture des deux côtés pour les documents recto-verso  
   e. Gestion des documents multiples (ex: passeport + carte d'identité)  

3. **Capture de Selfie Dynamique** :  
   a. Demande de mouvement spécifique (ex: clignement d'œil)  
   b. Vérification de la présence en temps réel (liveness detection)  
   c. Vérification de la concordance avec les documents fournis  
   d. Détection des tentatives de tromperie (photos, vidéos)  
   e. Génération d'un score de confiance biométrique  

4. **Vérification Automatisée par IA** :  
   a. Reconnaissance optique de caractères (OCR) pour extraire les données  
   b. Vérification de l'authenticité des documents (détecte les falsifications)  
   c. Comparaison des données entre documents (cohérence)  
   d. Analyse de la qualité des images (netteté, éclairage)  
   e. Détection de manipulations (coupures, collages)  

5. **Vérification Humaine si Nécessaire** :  
   a. Assignment à un vérificateur certifié  
   b. Vérification complète des documents  
   c. Comparaison avec les bases de données officielles  
   d. Validation des éléments douteux  
   e. Décision finale avec documentation  

6. **Validation Finale et Génération de la Carte Professionnelle Digitale** :  
   a. Génération d'un QR code sécurisé avec données chiffrées  
   b. Attribution d'un statut KYC (verified, pending, rejected)  
   c. Notification de l'utilisateur avec résultat  
   d. Mise à jour du profil utilisateur  
   e. Journalisation complète du processus  

**Critères de Validation KYC (Détail Technique)** :  

- **Correspondance entre Selfie et Pièce d'Identité** :  
  - Seuil de similarité : Min. 95%  
  - Méthode : Comparaison 3D des caractéristiques faciales  
  - Vérification liveness : Obligatoire  
  - Score de confiance : Calculé en temps réel  
  - Rejet automatique si score < seuil  

- **Validité des Documents** :  
  - Date d'expiration : Vérifiée automatiquement  
  - Authentification : Vérification de sécurité des documents  
  - Format : Conformité aux spécifications nationales  
  - Légibilité : OCR réussit à extraire les données  
  - Consistance : Absence de modifications évidentes  

- **Cohérence des Informations Fournies** :  
  - Correspondance entre les différents documents  
  - Cohérence avec les données fournies par l'utilisateur  
  - Absence de contradictions flagrantes  
  - Validation des informations de contact  
  - Cohérence avec les données contextuelles  

- **Absence de Drapeaux Rouges** :  
  - Vérification contre les listes de sanctions  
  - Analyse des antécédents (si disponible)  
  - Vérification de l'absence de signalements  
  - Analyse des comportements inhabituels  
  - Vérification des données avec les autorités compétentes  

### **6.2 Documentation et Vérification des Documents**

**Critères de Validation des Documents** :  

1. **Passeport** :  
   a. Format : Conforme aux spécifications ICAO  
   b. Données machine lisibles (MRZ) : Vérifiées  
   c. Photo : De bonne qualité, face visible, pas de lunettes  
   d. Date d'expiration : > 6 mois  
   e. Tampons d'entrée/sortie : Vérifiés pour la cohérence  
   f. Sécurité : Vérification des éléments de sécurité  

2. **Carte d'Identité Nationale** :  
   a. Format : Conforme aux spécifications nationales  
   b. Photo : De bonne qualité, face visible  
   c. Date d'expiration : Vérifiée  
   d. Signatures : Vérifiées si présentes  
   e. Éléments de sécurité : Vérifiés  
   f. Cohérence avec les autres documents  

3. **Permis de Conduire** :  
   a. Format : Conforme aux spécifications nationales  
   b. Photo : De bonne qualité, face visible  
   c. Date d'expiration : Vérifiée  
   d. Catégories : Vérifiées si pertinent  
   e. Éléments de sécurité : Vérifiés  
   f. Cohérence avec les autres documents  

4. **Autres Documents** :  
   a. Justificatif de domicile : Facture récente (< 3 mois)  
   b. Attestation de résidence : Si applicable  
   c. Document professionnel : Pour les partenaires  
   d. Certificats spécifiques : Selon le pays et le métier  

**Gestion des Rejets** :  

- **Motifs de Rejet Courants** :  
  - Document expiré ou proche de l'expiration  
  - Qualité insuffisante de l'image  
  - Incohérence entre les documents  
  - Doute sur l'authenticité  
  - Absence de données obligatoires  

- **Processus de Rejet** :  
  - Notification immédiate avec motif précis  
  - Guide détaillé pour corriger le problème  
  - Possibilité de soumettre des documents complémentaires  
  - Délai de réponse < 24h pour les vérifications standard  
  - Support dédié pour assistance  

- **Délais de Traitement** :  
  - Vérification automatique : < 2 minutes  
  - Vérification humaine standard : < 24h  
  - Cas complexes : < 72h  
  - Priorité aux comptes professionnels : Délai réduit de 50%  
  - Notification automatique à chaque étape  

### **6.3 Gestion des Documents KYC**

**Structure de Données (Détail Technique)** :  
```
kyc_documents
- id(UUID) PRIMARY KEY
- user_id(UUID) FOREIGN KEY
- document_type(enum: passport, national_id, driver_license, etc.)
- file_path(varchar)
- file_hash(varchar)
- status(enum: pending, verified, rejected)
- verification_date(timestamptz, nullable)
- verified_by(UUID, nullable)
- verification_method(enum: auto, manual)
- confidence_score(numeric, nullable)
- rejection_reason(text, nullable)
- created_at(timestamptz)
- updated_at(timestamptz)
- metadata(jsonb)
```

**Fonctionnalités de Gestion** :  

1. **Visualisation des Documents dans l'Interface Administrateur** :  
   a. Vue en grille des documents soumis  
   b. Filtres par statut, type de document, utilisateur  
   c. Zoom et rotation des images  
   d. Comparaison côte à côte des documents  
   e. Commentaires et annotations  

2. **Export des Documents pour Audit** :  
   a. Format PDF sécurisé avec watermark  
   b. Chiffrement AES-256 des documents exportés  
   c. Génération de rapports d'audit complets  
   d. Export en temps réel ou programmé  
   e. Journalisation des exports  

3. **Alertes Automatiques Avant Expérience des Documents** :  
   a. Configuration des seuils d'alerte (30, 15, 7 jours)  
   b. Notifications par email et dans l'interface  
   c. Possibilité de renouvellement en ligne  
   d. Gestion des délais selon les réglementations locales  
   e. Journalisation des alertes envoyées  

4. **Génération Automatique de Rappels de Renouvellement** :  
   a. Calcul des dates de renouvellement  
   b. Génération de rappels personnalisés  
   c. Gestion des différents types de documents  
   d. Adaptation aux réglementations locales  
   e. Option de désactivation par l'utilisateur  

**Spécifications de Sécurité** :  

- **Chiffrement des Documents** :  
  - AES-256 pour les documents au repos  
  - TLS 1.3 pour les documents en transit  
  - Chiffrement des métadonnées sensibles  
  - Gestion des clés via HashiCorp Vault  
  - Rotation des clés tous les 30 jours  

- **Journalisation** :  
  - Toutes les actions sur les documents sont enregistrées  
  - Horodatage précis avec fuseau horaire  
  - Informations sur l'utilisateur effectuant l'action  
  - Détails de l'action (type, cible, résultat)  
  - Conservation pendant 7 ans  

- **Accès** :  
  - Principe du moindre privilège strictement appliqué  
  - Authentification à deux facteurs pour les accès sensibles  
  - Revue des accès mensuelle pour les comptes administrateur  
  - Audit des accès trimestriel par un tiers indépendant  
  - Désactivation automatique après 30 minutes d'inactivité  

---

## **7. Système de Profil**

### **7.1 Structure de Données des Profils**

**Schéma de la Table Utilisateurs (Détail Technique)** :  
```
users
- id(UUID) PRIMARY KEY
- email(varchar(255) UNIQUE NOT NULL)
- phone(varchar(20) UNIQUE NOT NULL)
- password_hash(text NOT NULL)
- account_type(enum: client, partner, staff, commercial, admin) NOT NULL
- status(enum: pending, active, suspended, deleted) DEFAULT 'pending'
- created_at(timestamptz) DEFAULT CURRENT_TIMESTAMP
- last_login(timestamptz)
- security_level(int) DEFAULT 1
- country_code(varchar(2)) NOT NULL
- language(varchar(10)) DEFAULT 'fr'
- two_factor_enabled(boolean) DEFAULT false
- kyc_status(enum: none, pending, verified, rejected) DEFAULT 'none'
- kyc_rejection_reason(text)
- profile_data(jsonb) NOT NULL DEFAULT '{}'
- last_password_change(timestamptz)
- failed_login_attempts(int) DEFAULT 0
- lockout_until(timestamptz)
- verification_token(varchar(255))
- token_expiration(timestamptz)
```

**Structure de profile_data (Exemple pour Partenaire)** :  
```
{
  "business_name": "Nom de l'entreprise",
  "business_type": "Type d'entreprise",
  "rc_number": "Numéro de registre de commerce",
  "nif_number": "Numéro d'identification fiscale",
  "address": {
    "street": "Adresse",
    "city": "Ville",
    "postal_code": "Code postal",
    "country": "Pays"
  },
  "business_category": "Catégorie professionnelle",
  "description": "Description de l'entreprise",
  "social_links": {
    "facebook": "URL",
    "instagram": "URL",
    "linkedin": "URL"
  },
  "working_hours": [
    {
      "day": "Monday",
      "open": "08:00",
      "close": "18:00"
    }
  ],
  "profile_picture": "ID du document",
  "verification_status": "verified",
  "verification_date": "2023-01-15T12:30:00Z"
}
```

**Données Spécifiques par Type de Compte** :  

- **Client** :  
  - Historique des achats (produits, dates, montants)  
  - Adresses de livraison (principale, secondaires)  
  - Moyens de paiement (stockés de manière sécurisée)  
  - Préférences (langue, notifications, catégories préférées)  
  - Avis et notations (sur les professionnels)  
  - Historique de recherche (pour personnalisation)  

- **Partenaire** :  
  - Informations KYC complètes (documents, statut)  
  - Détails de l'entreprise (RC, NIF, etc.)  
  - Catalogue de services/produits (selon module)  
  - Historique des crédits ISMAIL (achats, consommation)  
  - Équipe associée (staff, commerciaux)  
  - Statistiques de performance (taux de conversion, satisfaction)  

- **Utilisateur d'Entreprise** :  
  - Business ID associé (référence à l'entreprise)  
  - Rôle spécifique au sein du business (admin, staff, etc.)  
  - Permissions granulaires (spécifiques à son rôle)  
  - Horaires de travail (personnalisés)  
  - Statut d'activité (actif, inactif, suspendu)  

**Structure de Données pour les Relations d'Entreprise** :  
```
business_users
- id(UUID) PRIMARY KEY
- business_id(UUID) REFERENCES businesses(id)
- user_id(UUID) REFERENCES users(id)
- role(enum: owner, admin, staff) NOT NULL
- permissions(jsonb) DEFAULT '{}'
- status(enum: active, inactive, suspended) DEFAULT 'active'
- joined_at(timestamptz) DEFAULT CURRENT_TIMESTAMP
- last_activity(timestamptz)

businesses
- id(UUID) PRIMARY KEY
- name(varchar(255)) NOT NULL
- rc_number(varchar(100)) UNIQUE
- nif_number(varchar(100)) UNIQUE
- legal_form(varchar(100))
- address(jsonb)
- created_at(timestamptz) DEFAULT CURRENT_TIMESTAMP
- status(enum: active, suspended, deleted) DEFAULT 'active'
- owner_id(UUID) REFERENCES users(id)
- category(varchar(100))
```

### **7.2 Gestion des Profils**

**Fonctionnalités Clés (Détail Technique)** :  

1. **Éditeur de Profil Complet avec Validation en Temps Réel** :  
   a. Interface utilisateur intuitive avec onboarding progressif  
   b. Validation en temps réel des champs (email, téléphone, etc.)  
   c. Aide contextuelle pour chaque champ  
   d. Gestion des erreurs détaillées  
   e. Enregistrement automatique des modifications  

2. **Historique des Modifications du Profil** :  
   a. Journalisation de chaque modification  
   b. Comparaison avant/après pour chaque champ modifié  
   c. Identification de l'utilisateur ayant effectué la modification  
   d. Horodatage précis de chaque modification  
   e. Possibilité de restauration des versions antérieures  

3. **Synchronisation des Données KYC avec le Profil** :  
   a. Mise à jour automatique des données KYC  
   b. Indicateur visuel du statut KYC  
   c. Processus de mise à jour en cas de changement de documents  
   d. Historique des validations KYC  
   e. Notification des mises à jour importantes  

4. **Personnalisation des Préférences Utilisateur** :  
   a. Configuration des notifications (canal, fréquence)  
   b. Sélection des langues préférées  
   c. Personnalisation de l'interface (thème, taille de police)  
   d. Gestion des données de partage  
   e. Options de confidentialité avancées  

5. **Gestion des Notifications par Canal** :  
   a. Configuration fine par type de notification  
   b. Priorisation des canaux (push, email, SMS)  
   c. Gestion des plages horaires de réception  
   d. Options de désactivation par type de notification  
   e. Journalisation des notifications envoyées  

**Sécurité (Détail Technique)** :  

- **Chiffrement des Données Sensibles** :  
  - AES-256 pour les données sensibles au repos  
  - TLS 1.3 pour les données en transit  
  - Chiffrement des métadonnées sensibles  
  - Gestion des clés via HashiCorp Vault  
  - Rotation des clés tous les 30 jours  

- **Journalisation de Toutes les Modifications** :  
  - Format structuré avec horodatage précis  
  - Identification de l'utilisateur effectuant la modification  
  - Détails de la modification (champ, ancienne valeur, nouvelle valeur)  
  - Stockage dans un système WORM pour immuabilité  
  - Conservation pendant 7 ans  

- **Vérification Multi-Facteurs pour les Modifications Sensibles** :  
  - Requise pour les modifications de données sensibles  
  - Processus d'authentification renforcée  
  - Journalisation de chaque vérification  
  - Délai de validité limité  
  - Possibilité de désactivation par l'administrateur  

- **Accès Restreint aux Données KYC Selon le Rôle** :  
  - RBAC stricte avec permissions granulaires  
  - Principe du moindre privilège appliqué  
  - Authentification à deux facteurs pour les accès sensibles  
  - Revue des accès mensuelle  
  - Audit trimestriel par un tiers indépendant  

---

## **8. Système Photo et Carte Professionnelle**

### **8.1 Génération de la Carte Professionnelle Digitale**

**Caractéristiques Techniques** :  

- **Format QR Code Intégré** :  
  - Type : QR Code Version 10 (101x101 modules)  
  - Correction d'erreur : Niveau H (30%)  
  - Encodage : UTF-8 avec compression  
  - Données stockées : Identifiant unique, statut KYC, date d'expiration  
  - Chiffrement : AES-256 avant génération du QR  

- **Données Stockées de Manière Sécurisée** :  
  - Identifiant utilisateur : UUID crypté  
  - Statut KYC : Validé par signature numérique  
  - Date d'expiration : 72h à partir de la génération  
  - Données de validation : Hash des documents KYC  
  - Clé de vérification : Unique par carte  

- **Vérification Instantanée via Scan** :  
  - Processus : Scan → Déchiffrement → Vérification de la signature  
  - Temps de vérification : < 1 seconde  
  - Connectivité : Fonctionne hors ligne avec données en cache  
  - Journalisation : Enregistrement de chaque vérification  
  - Sécurité : Aucune donnée sensible stockée sur le device  

**Processus de Génération (Détail Technique)** :  

1. **Validation KYC Terminée** :  
   a. Vérification du statut KYC (doit être "verified")  
   b. Vérification de l'absence de documents expirés  
   c. Vérification de la complétude du profil  
   d. Génération d'un événement "kyc.verified"  
   e. Initialisation du processus de génération  

2. **Génération des Données Chiffrées** :  
   a. Création des données utilisateur (identifiant, statut, etc.)  
   b. Application du chiffrement AES-256 avec clé unique  
   c. Signature numérique des données avec clé privée  
   d. Formatage des données pour le QR code  
   e. Vérification de l'intégrité des données  

3. **Création du QR Code Sécurisé** :  
   a. Génération du QR code avec les données chiffrées  
   b. Application d'un filigrane ISMAIL  
   c. Vérification de la lisibilité du QR code  
   d. Optimisation pour les appareils mobiles  
   e. Stockage sécurisé du QR code  

4. **Intégration dans l'Interface Utilisateur** :  
   a. Affichage dans le tableau de bord utilisateur  
   b. Possibilité de partage sécurisé  
   c. Gestion de l'expiration (72h)  
   d. Option d'impression haute résolution  
   e. Journalisation de chaque accès  

5. **Possibilité d'Impression ou de Partage Numérique** :  
   a. Format d'impression : PDF sécurisé avec watermark  
   b. Partage numérique : Via lien temporaire sécurisé  
   c. Durée de validité du partage : 72h  
   d. Journalisation des partages  
   e. Possibilité de révocation  

**Sécurité (Détail Technique)** :  

- **Chiffrement AES-256 des Données** :  
  - Algorithme : AES-256 en mode GCM  
  - Clés : Gérées via HashiCorp Vault  
  - Rotation : Tous les 30 jours  
  - Stockage : Clés ne quittent jamais le système de gestion  
  - Audit : Journalisation des accès aux clés  

- **Signature Cryptographique des Données** :  
  - Algorithme : ECDSA avec courbe secp256r1  
  - Clé privée : Stockée de manière sécurisée  
  - Clé publique : Distribuée via API sécurisée  
  - Validation : Vérification de l'intégrité des données  
  - Journalisation : Toutes les signatures sont enregistrées  

- **Durée de Vie Limitée des QR Codes** :  
  - Durée de validité : 72 heures  
  - Mécanisme de révocation : Via API  
  - Vérification de l'expiration : À chaque scan  
  - Journalisation : Date et heure de génération  
  - Renouvellement : Automatique avant expiration  

- **Journalisation Complète des Accès** :  
  - Format : JSON structuré avec horodatage précis  
  - Informations : Utilisateur, action, résultat  
  - Stockage : Système WORM pour immuabilité  
  - Conservation : 7 ans  
  - Accessibilité : Via API sécurisée  

### **8.2 Stockage et Gestion des Photos**

**Fonctionnalités Clés (Détail Technique)** :  

1. **Interface de Téléchargement de Photos** :  
   a. Glisser-déposer et sélection de fichiers  
   b. Vérification en temps réel de la qualité  
   c. Aide visuelle pour le cadrage  
   d. Prise en charge des formats JPG, PNG, WEBP  
   e. Limitation de la taille des fichiers  

2. **Redimensionnement Automatique pour les Terminaux Mobiles** :  
   a. Génération de versions adaptées (100x100, 300x300, 1200x1200)  
   b. Format WEBP pour l'optimisation  
   c. Compression intelligente selon la qualité du réseau  
   d. Conservation des métadonnées EXIF  
   e. Gestion des proportions (maintien du ratio)  

3. **Stockage Sécurisé dans le Système WORM** :  
   a. Chiffrement AES-256 avant stockage  
   b. Stockage triplement répliqué  
   c. Durée de conservation : 7 ans  
   d. Journalisation complète des accès  
   e. Système de protection contre les modifications  

4. **Gestion des Versions et Historique** :  
   a. Versioning automatique des photos  
   b. Comparaison avant/après  
   c. Possibilité de restauration  
   d. Journalisation des modifications  
   e. Gestion des métadonnées de chaque version  

5. **Interface de Prévisualisation** :  
   a. Vue en grille des photos  
   b. Zoom et rotation  
   c. Comparaison côte à côte  
   d. Gestion des descriptions  
   e. Options de partage sécurisé  

**Spécifications Techniques (Détail)** :  

- **Format : WebP pour Optimisation** :  
  - Qualité : 85% par défaut (ajustable)  
  - Support alpha : Activé pour les formats qui le permettent  
  - Taille réduite : ~30% par rapport à JPEG  
  - Compatibilité : Fallback automatique à JPEG pour les anciens navigateurs  
  - Génération dynamique : Selon le support du navigateur  

- **Taille Maximale : 5MB par Image** :  
  - Vérification avant téléchargement  
  - Avertissement en temps réel  
  - Compression automatique si dépassé  
  - Gestion des images haute résolution  
  - Options de réduction de qualité  

- **Redimensionnement : 1200x1200 Pixels Max** :  
  - Pour les images principales  
  - Versions adaptées pour les différents usages  
  - Maintien du ratio d'origine  
  - Gestion des images en format paysage/portrait  
  - Optimisation pour les différents modules  

- **Compression Intelligente selon la Qualité de la Connexion** :  
  - Détection de la qualité du réseau  
  - Ajustement dynamique de la qualité  
  - Priorité aux images critiques  
  - Gestion des mises en cache  
  - Options de téléchargement haute qualité  

---

## **9. Système de Portefeuille Électronique et Intégration Wave**

### **9.1 Architecture du Portefeuille**

```
+---------------------+      +---------------------+      +---------------------+
| Portefeuille        |<---->| API Wave            |<---->| Système de Crédits  |
| (Gestion des fonds) |      | (Intégration)       |      | (Gestion des crédits)|
+---------------------+      +---------------------+      +---------------------+
| - Solde             |      | - Authentification  |      | - Consommation      |
| - Transactions      |      | - Paiements         |      | - Achats            |
| - Historique        |      | - Statuts           |      | - Transactions      |
| - Sécurité          |      | - Callbacks         |      | - Reporting         |
+---------------------+      +---------------------+      +---------------------+
       ▲                          ▲                          ▲
       |                          |                          |
       |                          |                          |
       ▼                          ▼                          ▼
+---------------------+      +---------------------+      +---------------------+
| Base de Données     |      | Services Externes   |      | Base de Données     |
| (PostgreSQL Neon)   |      | (Wave API)          |      | (PostgreSQL Neon)   |
+---------------------+      +---------------------+      +---------------------+
```

**Flux de Paiement Détaillé** :  

1. **Le Partenaire Sélectionne un Forfait de Crédits ISMAIL** :  
   a. Interface de sélection des forfaits  
   b. Visualisation des avantages  
   c. Confirmation de la sélection  
   d. Génération de la requête de paiement  
   e. Journalisation de la sélection  

2. **Redirection vers l'Interface de Paiement Wave** :  
   a. Génération des paramètres de paiement  
   b. Signature des paramètres  
   c. Redirection sécurisée vers Wave  
   d. Gestion des erreurs de redirection  
   e. Journalisation de la redirection  

3. **Confirmation du Paiement par l'Utilisateur** :  
   a. Interface Wave avec données pré-remplies  
   b. Sélection du mode de paiement  
   c. Confirmation par l'utilisateur  
   d. Validation des données  
   e. Journalisation de l'initiation  

4. **Notification de Succès à ISMAIL** :  
   a. Callback Wave vers endpoint sécurisé  
   b. Validation de la signature  
   c. Vérification du statut de paiement  
   d. Journalisation de la notification  
   e. Gestion des notifications dupliquées  

5. **Mise à Jour Immédiate du Solde de Crédits** :  
   a. Vérification des données reçues  
   b. Mise à jour du solde utilisateur  
   c. Génération d'une transaction  
   d. Notification de succès à l'utilisateur  
   e. Journalisation de la mise à jour  

6. **Génération de la Facture Électronique** :  
   a. Génération de la facture PDF  
   b. Application de signature électronique  
   c. Stockage sécurisé de la facture  
   d. Envoi à l'utilisateur  
   e. Journalisation de la génération  

### **9.2 Intégration avec Wave**

**Processus de Paiement (Détail Technique)** :  

1. **Configuration des Flux de Paiement** :  
   a. Setup de l'API Wave avec clés d'API  
   b. Configuration des endpoints de callback  
   c. Définition des paramètres de paiement  
   d. Gestion des différents modes de paiement  
   e. Journalisation de la configuration  

2. **Mise en Place de la Clarification Essentielle** :  
   a. Documentation claire des flux  
   b. Formation des équipes support  
   c. Mise à jour des FAQ  
   d. Communication aux utilisateurs  
   e. Journalisation des clarifications  

3. **Les Clients Finaux N'ont Pas Accès au Portefeuille Électronique ISMAIL ni à l'Achat de Crédits** :  
   a. Validation côté serveur à chaque tentative d'accès  
   b. Gestion des rôles utilisateurs  
   c. Documentation des restrictions  
   d. Journalisation des tentatives non autorisées  
   e. Mise à jour régulière des politiques  

4. **Configuration des Interfaces de Paiement des Clients** :  
   a. Interface de paiement pour les transactions clients  
   b. Gestion des différents modes de paiement  
   c. Personnalisation selon le pays  
   d. Optimisation pour les terminaux mobiles  
   e. Journalisation des interactions  

5. **Développement des Mécanismes de Confirmation de Paiement** :  
   a. Callbacks Wave avec validation de signature  
   b. Gestion des statuts de paiement  
   c. Mécanismes de retry en cas d'échec  
   d. Journalisation détaillée  
   e. Alertes en cas de problème  

6. **Développement des Interfaces de Gestion des Transactions** :  
   a. Interface de consultation des transactions  
   b. Gestion des filtres et exports  
   c. Système de recherche avancée  
   d. Gestion des litiges  
   e. Journalisation des accès  

**Fonctionnalités Clés (Détail Technique)** :  

- **Support de Tous les Modes de Paiement Wave** :  
  - Mobile Money (MTN, Orange Money, etc.)  
  - Cartes bancaires  
  - Virement bancaire  
  - Paiement en espèces (via points de vente)  
  - Gestion des devises locales  

- **Confirmation en Temps Réel des Transactions** :  
  - Webhooks Wave avec validation  
  - Système de retry avec backoff exponentiel  
  - Journalisation détaillée des callbacks  
  - Alertes en temps réel pour les transactions sensibles  
  - Durée de confirmation : < 30 secondes  

- **Gestion des Erreurs et des Échecs de Paiement** :  
  - Codes d'erreur détaillés (Wave et ISMAIL)  
  - Mécanismes de résolution automatique  
  - Interface de gestion des erreurs  
  - Guide de résolution pour les utilisateurs  
  - Journalisation complète des erreurs  

- **Historique Complet des Transactions** :  
  - Stockage sécurisé des données  
  - Recherche avancée par critères  
  - Exports dans différents formats  
  - Conservation pendant 7 ans  
  - Journalisation des accès  

- **Facturation Électronique Automatisée** :  
  - Génération PDF avec modèle personnalisé  
  - Signature électronique  
  - Stockage sécurisé  
  - Envoi par email et dans l'interface  
  - Journalisation des générations  

### **9.3 Gestion des Transactions**

**Schéma de la Table Transaction (Détail Technique)** :  
```
transactions
- id(UUID) PRIMARY KEY
- partner_id(UUID) REFERENCES users(id) NOT NULL
- amount_credits(numeric(10,2)) NOT NULL
- operation_type(enum: debit, credit) NOT NULL
- module_source(varchar(50)) NOT NULL
- feature_used(varchar(100)) NOT NULL
- timestamp(timestamptz) DEFAULT CURRENT_TIMESTAMP
- status(enum: pending, completed, failed, reversed) NOT NULL
- reference(varchar(100))
- hash(varchar(100)) NOT NULL
- created_at(timestamptz) DEFAULT CURRENT_TIMESTAMP
- updated_at(timestamptz) DEFAULT CURRENT_TIMESTAMP
- metadata(jsonb)
```

**Exemple de Données** :  
```
{
  "id": "a1b2c3d4-5678-90ab-cdef-1234567890ab",
  "partner_id": "e1f2a3b4-5678-90ab-cdef-1234567890cd",
  "amount_credits": 1500.00,
  "operation_type": "credit",
  "module_source": "shop",
  "feature_used": "product_listing",
  "timestamp": "2023-01-15T12:30:00Z",
  "status": "completed",
  "reference": "WAVE-20230115-123456",
  "hash": "sha256:abc123...",
  "metadata": {
    "payment_method": "mobile_money",
    "country": "SN",
    "transaction_id": "123456789",
    "fee": 150.00
  }
}
```

**Spécifications de Performance (Détail Technique)** :  

- **Traitement de 5,000 Transactions/Secondes** :  
  - Architecture scalable avec auto-scaling  
  - Base de données optimisée pour les écritures  
  - Queue de messages pour la décentralisation  
  - Système de caching stratégique  
  - Monitoring en temps réel  

- **Journalisation Immuable avec Vérification Cryptographique** :  
  - Chiffrement AES-256 des données  
  - Signature numérique de chaque transaction  
  - Stockage dans un système WORM  
  - Vérification de l'intégrité  
  - Conservation pendant 7 ans  

- **Durée de Vie des Transactions en Attente** :  
  - Durée maximale : 15 minutes  
  - Mécanisme de nettoyage automatique  
  - Journalisation des transactions expirées  
  - Possibilité de prolongation  
  - Gestion des cas spéciaux  

- **Système de Notification en Temps Réel des Transactions** :  
  - Webhooks vers les modules concernés  
  - Système de retry avec backoff exponentiel  
  - Journalisation des notifications  
  - Alertes en cas d'échec  
  - Durée de traitement : < 2 secondes  

---

## **10. Système Intégral des Crédits**

### **10.1 Modèle Économique des Crédits**

**Principe Central (Détail Commercial)** :  

- **1 crédit ISMAIL = 100 F CFA** :  
  - Taux fixe indépendamment du pays  
  - Conversion automatique selon la devise locale  
  - Marge de profit : 23.5% (coût moyen 65 F CFA)  
  - Transparence absolue pour les utilisateurs  
  - Journalisation des taux de change  

- **Système de Crédits Prépayés pour les Partenaires** :  
  - Achat en avance par les partenaires  
  - Utilisation pour accéder aux fonctionnalités  
  - Pas de crédit ou de compte à terme  
  - Gestion stricte des soldes  
  - Alertes de seuil bas  

- **Monétisation via l'Achat de Crédits et leur Consommation** :  
  - Revenus directs : Vente de crédits  
  - Revenus indirects : Services premium  
  - Modèle de revenu récurrent  
  - Analyse prédictive des besoins  
  - Gestion des forfaits  

- **Gestion Transversale des Crédits sur Tous les Modules** :  
  - Un seul portefeuille crédits  
  - Utilisation sur tous les modules  
  - Gestion centralisée des soldes  
  - Reporting consolidé  
  - Journalisation détaillée  

**Forfaits Disponibles (Détail Commercial)** :  

| Forfait | Crédits | Prix (F CFA) | Économie | Public Cible | Avantages |
|---------|---------|--------------|----------|--------------|-----------|
| Starter | 1,000   | 95,000       | 5%       | Nouveaux partenaires | Guide de démarrage, support prioritaire |
| Basic   | 5,000   | 450,000      | 10%      | PME en croissance | Analytique avancée, priorité dans les recherches |
| Pro     | 15,000  | 1,200,000    | 15%      | Partenaires performants | Support 24/7, personnalisation interface |
| Enterprise | 50,000+ | Sur devis  | 25%      | Grands partenaires | API dédiée, SLA personnalisé, gestion de compte dédiée |

**Politique de Tarification** :  

- **Formule Mathématique d'Économie** :  
  ```
  Prix_effectif = Crédits * 100 * (1 - Taux_réduction)
  Taux_réduction = 0.04 + (0.21 * ln(Crédits/500))
  ```

- **Offre de Démarrage** :  
  - 50 crédits gratuits pour les 10 premiers leads  
  - Valable pendant 30 jours après inscription  
  - Non cumulable avec d'autres promotions  
  - Gestion centralisée des offres  

- **Tarification Progressive** :  
  - Réduction de 20% sur les leads au-delà de 50/mois  
  - Bonus de 5% pour les achats mensuels répétés  
  - Offres spéciales selon la saisonnalité  
  - Programmes de fidélité  

### **10.2 Mécanismes de Consommation**

**Calcul des Crédits (Détail Technique)** :  

- **Formule Dynamique Selon le Module et la Fonctionnalité** :  
  ```
  Consommation = (Nombre de produits × 0.5) + (Nombre de commandes × 1) + (Nombre de messages × 0.1)
  ```

- **Exemple pour ISMAIL Shop** :  
  ```
  Consommation = (Nb_produits × 0.5) + (Nb_commandes × 1) + (Nb_messages × 0.1)
  ```

- **Exemple pour ISMAIL Services** :  
  ```
  Consommation = (Nb_leads_qualifiés × 1) + (Nb_devis_envoyés × 0.5) + (Nb_contrats_conclus × 5) + (Nb_jours_boost × 0.6)
  ```

- **Exemple pour ISMAIL Booking** :  
  ```
  Consommation = (Nb_réservations × 2) + (Nb_demandes_spéciales × 1) + (Nb_jours_boost × 0.8)
  ```

- **Exemple pour ISMAIL Immobilier** :  
  ```
  Consommation = (Nb_annonces × 5) + (Nb_visites_programmées × 2) + (Nb_visites_réalisées × 1) + (Nb_jours_boost × 1.2)
  ```

**Forfaits Spéciaux (Détail Commercial)** :  

- **"Boost de Visibilité"** :  
  - 10 crédits par heure de visibilité accrue  
  - Disponible pour tous les modules  
  - Durée maximale par boost : 24h  
  - Priorité dans les résultats de recherche  
  - Gestion centralisée des boosts  

- **"Forfait Livreur Régulier"** :  
  - 100 crédits/jour pour un volume illimité  
  - Valable pour ISMAIL Livraisons  
  - Minimum de 30 livraisons/jour  
  - Réduction de 20% pour engagement mensuel  
  - Gestion des périodes d'inactivité  

- **"Forfait Abonnement"** :  
  - Réduction de 15% sur les achats mensuels  
  - Engagement minimum de 3 mois  
  - Paiement mensuel automatique  
  - Gestion des annulations  
  - Reporting mensuel consolidé  

**Gestion des Crédits Réservés** :  

- **Réservation Avant Consommation** :  
  - Crédits réservés lors de l'initiation d'une action  
  - Libération automatique en cas d'annulation  
  - Durée de réservation : 15 minutes  
  - Gestion des cas de latence réseau  
  - Journalisation détaillée  

- **Validation Finale** :  
  - Consommation définitive après confirmation  
  - Mise à jour du solde en temps réel  
  - Génération d'une transaction  
  - Notification de consommation  
  - Journalisation complète  

- **Gestion des Annulations** :  
  - Remboursement automatique des crédits réservés  
  - Durée de remboursement : < 5 minutes  
  - Notification de remboursement  
  - Journalisation des annulations  
  - Gestion des exceptions  

### **10.3 Journalisation et Suivi des Crédits**

**Tableau de Bord des Crédits (Détail Technique)** :  

1. **Solde Actuel en Temps Réel** :  
   a. Affichage clair du solde  
   b. Indicateur de la prochaine échéance  
   c. Comparaison avec le mois précédent  
   d. Visualisation graphique de l'historique  
   e. Options de recharge  

2. **Historique des Transactions** :  
   a. Liste chronologique des transactions  
   b. Filtres par type, module, date  
   c. Recherche avancée  
   d. Export dans différents formats  
   e. Conservation pendant 3 ans  

3. **Prévision de Consommation** :  
   a. Analyse des tendances de consommation  
   b. Prédiction pour les 30 prochains jours  
   c. Alertes de seuil bas  
   d. Comparaison avec des partenaires similaires  
   e. Options de simulation  

4. **Alertes de Seuil Bas** :  
   a. Configuration personnalisée (10% et 5%)  
   b. Notifications par canal préféré  
   c. Actions recommandées  
   d. Historique des alertes  
   e. Désactivation temporaire possible  

5. **Analyse des Tendances de Consommation** :  
   a. Graphiques interactifs des tendances  
   b. Comparaison par module  
   c. Identification des pics de consommation  
   d. Suggestions d'optimisation  
   e. Reporting personnalisé  

**Sécurité (Détail Technique)** :  

- **Journalisation Immuable de Toutes les Transactions** :  
  - Format structuré avec horodatage précis  
  - Signature numérique de chaque transaction  
  - Stockage dans un système WORM  
  - Vérification de l'intégrité  
  - Conservation pendant 7 ans  

- **Vérification Cryptographique des Transactions** :  
  - Algorithme : SHA-256  
  - Signature : Avec clé privée  
  - Vérification : À chaque consultation  
  - Journalisation des vérifications  
  - Gestion des clés de signature  

- **Audit Mensuel des Transactions** :  
  - Vérification de la cohérence des données  
  - Analyse des anomalies  
  - Rapport de conformité  
  - Recommandations d'amélioration  
  - Journalisation des audits  

- **Protection Contre les Tentatives de Fraude** :  
  - Détection des schémas de consommation anormaux  
  - Limites de consommation par période  
  - Validation des transactions sensibles  
  - Mécanismes de verification en temps réel  
  - Journalisation des alertes  

---

## **11. Système de Gestion des Utilisateurs et Administration**

### **11.1 Modèle RBAC Avancé**

**Hiérarchie des Rôles (Détail Technique)** :  

1. **Administrateur** :  
   a. Accès complet à tous les modules  
   b. Gestion des paramètres globaux  
   c. Gestion des rôles et permissions  
   d. Accès aux journaux système  
   e. Gestion des configurations critiques  

2. **Gestionnaire de Contenu** :  
   a. Gestion des contenus et modération  
   b. Validation des annonces et services  
   c. Gestion des politiques de contenu  
   d. Reporting sur la qualité du contenu  
   e. Gestion des alertes de contenu  

3. **Commerciaux** :  
   a. Gestion des partenaires et suivi des ventes  
   b. Gestion des leads et prospection  
   c. Suivi des objectifs commerciaux  
   d. Reporting sur les performances  
   e. Gestion des relations clients  

4. **Partenaire** :  
   a. Accès à son espace de gestion  
   b. Gestion de ses produits/services  
   c. Gestion de ses transactions  
   d. Accès aux données de performance  
   e. Gestion de son équipe  

5. **Client** :  
   a. Accès aux fonctionnalités de consommation  
   b. Gestion de ses commandes  
   c. Gestion de ses préférences  
   d. Accès aux données personnelles  
   e. Gestion des avis et notations  

**Permissions Granulaires (Détail Technique)** :  

- **Configuration des Permissions par Module** :  
  a. Interface de gestion des permissions  
  b. Attribution de permissions individuelles  
  c. Groupement des permissions par fonctionnalité  
  d. Visualisation des permissions par rôle  
  e. Journalisation des modifications  

- **Définition des Actions Autorisées par Rôle** :  
  a. Listage des actions possibles  
  b. Association des actions aux rôles  
  c. Gestion des exceptions  
  d. Validation des configurations  
  e. Documentation des permissions  

- **Gestion des Exceptions et Surcharges de Permissions** :  
  a. Attribution de permissions spécifiques  
  b. Gestion des priorités entre rôles  
  c. Journalisation des exceptions  
  d. Revue périodique des exceptions  
  e. Documentation des surcharges  

- **Interface de Gestion des Permissions Intuitive** :  
  a. Vue en arbre des permissions  
  b. Recherche avancée  
  c. Comparaison entre rôles  
  d. Gestion par drag&drop  
  e. Export/Import des configurations  

**Structure de Données RBAC (Détail Technique)** :  
```
roles
- id(UUID) PRIMARY KEY
- name(varchar(50)) UNIQUE NOT NULL
- description(text)
- is_default(boolean) DEFAULT false
- created_at(timestamptz) DEFAULT CURRENT_TIMESTAMP
- updated_at(timestamptz) DEFAULT CURRENT_TIMESTAMP

permissions
- id(UUID) PRIMARY KEY
- name(varchar(100)) UNIQUE NOT NULL
- description(text)
- module(varchar(50)) NOT NULL
- created_at(timestamptz) DEFAULT CURRENT_TIMESTAMP
- updated_at(timestamptz) DEFAULT CURRENT_TIMESTAMP

role_permissions
- role_id(UUID) REFERENCES roles(id)
- permission_id(UUID) REFERENCES permissions(id)
- PRIMARY KEY(role_id, permission_id)

user_roles
- user_id(UUID) REFERENCES users(id)
- role_id(UUID) REFERENCES roles(id)
- assigned_by(UUID) REFERENCES users(id)
- assigned_at(timestamptz) DEFAULT CURRENT_TIMESTAMP
- PRIMARY KEY(user_id, role_id)
```

### **11.2 Gestion des Profils Utilisateurs**

**Fonctionnalités (Détail Technique)** :  

1. **Interface de Gestion des Utilisateurs** :  
   a. Vue en grille des utilisateurs  
   b. Filtres avancés par critères  
   c. Recherche full-text  
   d. Tri par colonne  
   e. Pagination et export  

2. **Filtrage et Recherche Avancée** :  
   a. Critères multiples combinables  
   b. Recherche par texte libre  
   c. Filtres par statut, rôle, date  
   d. Sauvegarde des vues personnalisées  
   e. Export des résultats  

3. **Mass Actions (Suspension, Activation, Changement de Rôle)** :  
   a. Sélection multiple d'utilisateurs  
   b. Actions groupées avec confirmation  
   c. Journalisation des actions  
   d. Options de undo  
   e. Reporting des résultats  

4. **Historique Complet des Actions** :  
   a. Journalisation de toutes les modifications  
   b. Comparaison avant/après  
   c. Identification de l'utilisateur effectuant l'action  
   d. Horodatage précis  
   e. Conservation pendant 7 ans  

5. **Export des Données pour Analyse** :  
   a. Formats supportés : CSV, Excel, PDF  
   b. Personnalisation des colonnes  
   c. Filtres appliqués avant export  
   d. Gestion des données sensibles  
   e. Journalisation des exports  

**Sécurité (Détail Technique)** :  

- **Journalisation de Toutes les Modifications** :  
  a. Format structuré avec horodatage précis  
  b. Identification de l'utilisateur effectuant la modification  
  c. Détails de la modification (champ, ancienne valeur, nouvelle valeur)  
  d. Stockage dans un système WORM  
  e. Conservation pendant 7 ans  

- **Vérification Multi-Facteurs pour les Actions Sensibles** :  
  a. Requise pour les modifications critiques  
  b. Processus d'authentification renforcée  
  c. Journalisation de chaque vérification  
  d. Délai de validité limité  
  e. Possibilité de désactivation par l'administrateur  

- **Accès Restreint selon le Principe du Moindre Privilège** :  
  a. RBAC stricte avec permissions granulaires  
  b. Principe du moindre privilège appliqué  
  c. Authentification à deux facteurs pour les accès sensibles  
  d. Revue des accès mensuelle  
  e. Audit trimestriel par un tiers indépendant  

- **Audit Régulier des Permissions** :  
  a. Revue automatique mensuelle  
  b. Rapport d'audit détaillé  
  c. Recommandations d'amélioration  
  d. Journalisation des audits  
  e. Suivi des actions correctives  

### **11.3 Administration de la Plateforme**

**Interface d'Administration ISMAIL (Détail Technique)** :  

1. **Tableaux de Bord de Performance Globale** :  
   a. Vue consolidée des KPI principaux  
   b. Graphiques interactifs  
   c. Comparaison avec les mois précédents  
   d. Alertes visuelles pour les seuils critiques  
   e. Options de personnalisation  

2. **Gestion des Modules et Fonctionnalités** :  
   a. Activation/désactivation des modules  
   b. Configuration des paramètres  
   c. Gestion des mises à jour  
   d. Surveillance des performances  
   e. Journalisation des modifications  

3. **Configuration des Paramètres Globaux** :  
   a. Paramètres de sécurité  
   b. Paramètres de localisation  
   c. Paramètres de monétisation  
   d. Paramètres de notification  
   e. Journalisation des changements  

4. **Gestion des Mises à Jour et Correctifs** :  
   a. Planification des déploiements  
   b. Gestion des versions  
   c. Journalisation des correctifs  
   d. Options de rollback  
   e. Validation des correctifs  

5. **Journalisation et Audit Centralisé** :  
   a. Tableaux de bord d'audit  
   b. Recherche avancée dans les journaux  
   c. Alertes en temps réel  
   d. Reporting personnalisé  
   e. Export des données d'audit  

**Fonctionnalités Clés (Détail Technique)** :  

- **Configuration des Règles de Consommation de Crédits** :  
  a. Interface de gestion des formules  
  b. Tests des formules avant activation  
  c. Journalisation des modifications  
  d. Options de rollback  
  e. Documentation des règles  

- **Gestion des Tarifs et Forfaits** :  
  a. Configuration des forfaits  
  b. Gestion des tarifs par pays  
  c. Journalisation des modifications  
  d. Options de test  
  e. Reporting des impacts  

- **Configuration des Règles KYC par Pays** :  
  a. Paramétrage des documents requis  
  b. Configuration des seuils de vérification  
  c. Journalisation des modifications  
  d. Adaptation aux réglementations locales  
  e. Documentation des règles  

- **Gestion des Intégrations Tierces** :  
  a. Configuration des API externes  
  b. Gestion des clés d'API  
  c. Journalisation des appels  
  d. Monitoring des performances  
  e. Documentation des intégrations  

- **Surveillance des Performances Système** :  
  a. Tableaux de bord en temps réel  
  b. Alertes de seuil critique  
  c. Analyse des tendances  
  d. Reporting personnalisé  
  e. Journalisation des incidents  

---

## **12. Système de Gestion Commerciale**

### **12.1 Structure Hiérarchique des Équipes Commerciales**

**Niveaux Hiérarchiques (Détail Opérationnel)** :  

1. **Recruteur** :  
   a. Recrutement de nouveaux partenaires  
   b. Gestion de 50-100 partenaires  
   c. Taux de conversion cible : > 30%  
   d. Formation : 2 semaines intensives  
   e. Outils : CRM, outils de prospection  

2. **Gestionnaire** :  
   a. Accompagnement des partenaires  
   b. Gestion de 20-30 partenaires  
   c. Taux de rétention cible : > 85%  
   d. Formation : 1 mois (1 semaine théorie, 3 semaines pratique)  
   e. Outils : CRM, outils d'analyse  

3. **Chef de Secteur** :  
   a. Gestion d'une zone géographique  
   b. Gestion de 3-5 équipes de gestionnaires  
   c. Taux de croissance cible : > 15% mensuel  
   d. Formation : 2 mois (1 mois théorie, 1 mois pratique)  
   e. Outils : Tableaux de bord avancés  

4. **Directeur Commercial** :  
   a. Gestion de la zone régionale  
   b. Gestion de 5-10 chefs de secteur  
   c. Taux de croissance cible : > 20% mensuel  
   d. Formation : 3 mois (1 mois théorie, 2 mois pratique)  
   e. Outils : Reporting stratégique  

5. **Directeur Général des Ventes** :  
   a. Direction nationale  
   b. Gestion de 3-5 directeurs commerciaux  
   c. Taux de croissance cible : > 25% mensuel  
   d. Formation : 6 mois (2 mois théorie, 4 mois pratique)  
   e. Outils : Vue consolidée nationale  

**Critères d'Avancement (Détail Opérationnel)** :  

- **Nombre de Partenaires Recrutés (Objectif Mensuel)** :  
  a. Recruteur : 15-20 partenaires/mois  
  b. Gestionnaire : 5-10 partenaires/mois (en plus de l'accompagnement)  
  c. Chef de Secteur : 20-30 partenaires/mois (via ses équipes)  
  d. Directeur Commercial : 50-70 partenaires/mois  
  e. Directeur Général : 150-200 partenaires/mois  

- **Taux de Rétention des Partenaires** :  
  a. Seuil minimum : > 85% à 6 mois  
  b. Évaluation mensuelle  
  c. Gestion des départs  
  d. Analyse des motifs de départ  
  e. Actions correctives  

- **Chiffre d'Affaires Généré** :  
  a. Objectif mensuel par niveau  
  b. Calcul basé sur les crédits consommés  
  c. Analyse par module  
  d. Comparaison avec les objectifs  
  e. Reporting détaillé  

- **Feedback des Partenaires** :  
  a. Enquête de satisfaction mensuelle  
  b. Score minimum : 4/5  
  c. Analyse des commentaires  
  d. Actions correctives  
  e. Journalisation des feedbacks  

- **Respect des Délais et Procédures** :  
  a. Respect des processus de recrutement  
  b. Respect des délais de reporting  
  c. Respect des procédures KYC  
  d. Audit mensuel des conformités  
  e. Journalisation des écarts  

### **12.2 Calcul des Commissions**

**Formule de Calcul (Détail Mathématique)** :  
```
Commission = (CA généré × Taux de commission) + Bonus de performance
```

**Taux de Commission (Détail Commercial)** :  

- **Niveau 1 (Recruteur)** :  
  a. Base : 5% du CA généré  
  b. Plafond : Aucun  
  c. Calcul : Sur le CA généré par les partenaires recrutés pendant 12 mois  
  d. Périodicité : Mensuelle  
  e. Retenue à la source : Appliquée selon la législation locale  

- **Niveau 2 (Gestionnaire)** :  
  a. Base : 3% du CA généré  
  b. Complément : 0.5% sur les partenaires du niveau 1  
  c. Plafond : Aucun  
  d. Calcul : Sur le CA généré par les partenaires gérés  
  e. Périodicité : Mensuelle  
  f. Retenue à la source : Appliquée selon la législation locale  

- **Niveau 3 (Chef de Secteur)** :  
  a. Base : 2% du CA généré  
  b. Complément : 0.3% sur les partenaires des niveaux 1 et 2  
  c. Plafond : Aucun  
  d. Calcul : Sur le CA généré par les secteurs gérés  
  e. Périodicité : Mensuelle  
  f. Retenue à la source : Appliquée selon la législation locale  

- **Niveau 4 (Directeur Commercial)** :  
  a. Base : 1.5% du CA généré  
  b. Complément : 0.2% sur les partenaires des niveaux inférieurs  
  c. Plafond : Aucun  
  d. Calcul : Sur le CA généré par la région gérée  
  e. Périodicité : Mensuelle  
  f. Retenue à la source : Appliquée selon la législation locale  

- **Niveau 5 (Directeur Général)** :  
  a. Base : 1% du CA généré  
  b. Complément : 0.1% sur les partenaires de tous les niveaux  
  c. Plafond : Aucun  
  d. Calcul : Sur le CA généré au niveau national  
  e. Périodicité : Mensuelle  
  f. Retenue à la source : Appliquée selon la législation locale  

**Bonus de Performance (Détail Commercial)** :  

- **5% Supplémentaire pour un Taux de Rétention > 90%** :  
  a. Calcul basé sur le taux de rétention à 6 mois  
  b. Application mensuelle  
  c. Plafond : 5% du montant de base  
  d. Documentation : Rapport de rétention  

- **3% Supplémentaire pour un CA Supérieur à l'Objectif de 20%** :  
  a. Calcul sur l'excédent de CA  
  b. Application mensuelle  
  c. Plafond : 3% du montant de base  
  d. Documentation : Rapport de CA  

- **2% Supplémentaire pour un Taux de Satisfaction Partenaire > 4.5/5** :  
  a. Calcul basé sur les enquêtes de satisfaction  
  b. Application mensuelle  
  c. Plafond : 2% du montant de base  
  d. Documentation : Rapport de satisfaction  

**Structure de Données des Commissions (Détail Technique)** :  
```
commissions
- id(UUID) PRIMARY KEY
- commercial_id(UUID) REFERENCES users(id)
- period(varchar(7)) NOT NULL -- Format YYYY-MM
- ca_generated(numeric(15,2)) NOT NULL
- base_commission(numeric(15,2)) NOT NULL
- performance_bonus(numeric(15,2)) DEFAULT 0
- total_commission(numeric(15,2)) NOT NULL
- status(enum: pending, approved, paid) DEFAULT 'pending'
- payment_date(timestamptz)
- created_at(timestamptz) DEFAULT CURRENT_TIMESTAMP
- updated_at(timestamptz) DEFAULT CURRENT_TIMESTAMP
- metadata(jsonb)
```

**Processus de Calcul (Détail Technique)** :  

1. **Collecte des Données de CA** :  
   a. Agrégation des transactions par commercial  
   b. Vérification de la qualité des données  
   c. Application des règles de calcul  
   d. Génération des montants bruts  
   e. Journalisation des données brutes  

2. **Calcul de la Commission de Base** :  
   a. Application des taux de commission  
   b. Vérification des seuils  
   c. Calcul des compléments hiérarchiques  
   d. Génération du montant de base  
   e. Journalisation du calcul  

3. **Calcul des Bonus de Performance** :  
   a. Analyse des indicateurs de performance  
   b. Vérification des seuils  
   c. Calcul des bonus  
   d. Génération du montant total  
   e. Journalisation des bonus  

4. **Validation et Approbation** :  
   a. Revue par le manager hiérarchique  
   b. Validation des calculs  
   c. Correction des erreurs éventuelles  
   d. Approbation finale  
   e. Journalisation de l'approbation  

5. **Paiement** :  
   a. Génération des ordres de paiement  
   b. Vérification des données bancaires  
   c. Exécution des paiements  
   d. Confirmation des paiements  
   e. Journalisation des paiements  

### **12.3 Processus d'Onboarding Commercial**

**Étapes Clés (Détail Opérationnel)** :  

1. **Formation Initiale (1 Semaine)** :  
   a. Jour 1-2 : Présentation de la plateforme  
   b. Jour 3-4 : Formation aux modules métier  
   c. Jour 5 : Pratique guidée avec simulations  
   d. Évaluation finale : Test théorique et pratique  
   e. Attestation de formation  

2. **Attribut d'un Mentor (1 Mois)** :  
   a. Attribution d'un commercial expérimenté  
   b. Sessions hebdomadaires de suivi  
   c. Gestion des doutes et questions  
   d. Partage de bonnes pratiques  
   e. Évaluation mensuelle  

3. **Accès Partiel à l'Interface (2 Mois)** :  
   a. Accès limité aux fonctionnalités de base  
   b. Surveillance par le mentor  
   c. Progression vers plus de fonctionnalités  
   d. Évaluation hebdomadaire  
   e. Reporting mensuel  

4. **Accès Complet Après Validation (3 Mois)** :  
   a. Évaluation finale par le manager  
   b. Obtention des accès complets  
   c. Intégration dans l'équipe commerciale  
   d. Suivi de performance  
   e. Reporting consolidé  

**Support Continu (Détail Opérationnel)** :  

- **Réunions de Pilotage Quotidiennes pour les Indicateurs Opérationnels** :  
  a. Durée : 15 minutes  
  b. Participants : Commercial et mentor  
  c. Sujets : Objectifs de la journée, obstacles, réussites  
  d. Outils : Tableau de bord en temps réel  
  e. Journalisation : Notes de réunion  

- **Reporting Mensuel avec Analyse Stratégique** :  
  a. Préparation : 2 jours avant la réunion  
  b. Analyse : Tendances, forces, faiblesses  
  c. Recommandations : Actions correctives  
  d. Suivi : Actions décidées  
  e. Documentation : Rapport complet  

- **Alertes Automatisées pour les Seuils Critiques** :  
  a. Configuration personnalisée  
  b. Notifications par canal préféré  
  c. Actions recommandées  
  d. Historique des alertes  
  e. Désactivation temporaire possible  

- **Documentation des Décisions et de leur Impact** :  
  a. Format structuré  
  b. Horodatage précis  
  c. Participants et décisions  
  d. Actions décidées  
  e. Suivi des impacts  

---

## **13. Modèle Économique Général**

### **13.1 Principe Central**

**Principe Central (Détail Commercial)** :  

- **Pour les Partenaires** : Achat de crédits ISMAIL utilisables sur tous les modules  
  - Modèle prépayé : Achat avant utilisation  
  - Prix fixe : 1 crédit = 100 F CFA  
  - Gestion centralisée des crédits  
  - Transparence absolue sur la consommation  
  - Journalisation détaillée des transactions  

- **Pour les Clients** : Paiements directs via les solutions de paiement locales (Wave, MTN Money, etc.)  
  - Paiements en temps réel  
  - Diversité des méthodes de paiement  
  - Intégration avec les solutions locales  
  - Sécurité renforcée  
  - Journalisation des transactions  

**Avantages du Modèle (Détail Commercial)** :  

- **Simplification des Transactions entre Partenaires** :  
  a. Un seul système de paiement  
  b. Aucun besoin de conversion de devises  
  c. Transactions instantanées  
  d. Réduction des coûts de transaction  
  e. Gestion centralisée  

- **Réduction des Coûts de Gestion des Paiements** :  
  a. Économies d'échelle  
  b. Automatisation des processus  
  c. Réduction des erreurs humaines  
  d. Gestion centralisée des factures  
  e. Reporting consolidé  

- **Création de Revenus Récurrents Prévisibles** :  
  a. Revenus réguliers grâce aux crédits  
  b. Analyse prédictive des besoins  
  c. Planification budgétaire améliorée  
  d. Réduction des pics de trésorerie  
  e. Meilleure visibilité financière  

- **Renforcement de la Fidélisation des Partenaires** :  
  a. Programmes de fidélité  
  b. Offres personnalisées  
  c. Analyse prédictive des besoins  
  d. Gestion proactive des relations  
  e. Meilleure expérience utilisateur  

### **13.2 Formules d'Achat et de Consommation**

**Forfaits d'Achat (Détail Commercial)** :  

| Forfait | Crédits | Prix (F CFA) | Prix par crédit | Économie | Public Cible | Avantages |
|---------|---------|--------------|-----------------|----------|--------------|-----------|
| Starter | 500     | 48,000       | 96 F CFA        | 4%       | Nouveaux partenaires | Guide de démarrage |
| Basic   | 1,500   | 135,000      | 90 F CFA        | 10%      | PME en croissance | Analytique avancée |
| Pro     | 5,000   | 425,000      | 85 F CFA        | 15%      | Partenaires actifs | Support prioritaire |
| Enterprise | 15,000 | 1,125,000    | 75 F CFA        | 25%      | Grands partenaires | API dédiée, SLA personnalisé |

**Formule Mathématique d'Économie (Détail Technique)** :  
```
Prix_effectif = Crédits * 100 * (1 - Taux_réduction)
Taux_réduction = 0.04 + (0.21 * ln(Crédits/500))
```

**Formules de Consommation (Détail Technique)** :  

- **ISMAIL Shop** :  
  ```
  Consommation = (Nb_produits × 0.5) + (Nb_commandes × 1) + (Nb_messages × 0.1)
  ```

- **ISMAIL Services** :  
  ```
  Consommation = (Nb_leads × 1) + (Nb_devis × 0.5) + (Nb_contrats × 5) + (Nb_boost × 0.6)
  ```

- **ISMAIL Booking** :  
  ```
  Consommation = (Nb_réservations × 2) + (Nb_demandes_spéciales × 1) + (Nb_boost × 0.8)
  ```

- **ISMAIL Immobilier** :  
  ```
  Consommation = (Nb_annonces × 5) + (Nb_visites × 2) + (Nb_visites_réalisées × 1) + (Nb_boost × 1.2)
  ```

- **ISMAIL Recouvrement** :  
  ```
  Consommation = (1 × Dossier) + (2 × Relance) + (5 × Procédure_judiciaire) + (0.5 × %_recouvré)
  ```

- **ISMAIL Livraisons** :  
  ```
  Consommation = (1 × Commande) + (0.5 × Relance_livraison) + (3 × Problème_livraison)
  ```

- **ISMAIL Location** :  
  ```
  Consommation = (2 × Réservation) + (1 × Relance) + (5 × Problème)
  ```

- **ISMAIL VTC** :  
  ```
  Consommation = (1 × Course) + (0.5 × Relance) + (3 × Problème)
  ```

- **ISMAIL Education** :  
  ```
  Consommation = (5 × Cours) + (1 × Étudiant) + (2 × Gestion)
  ```

- **ISMAIL Santé** :  
  ```
  Consommation = (2 × Rendez-vous) + (1 × Dossier) + (3 × Gestion)
  ```

- **ISMAIL Téléphonie** :  
  ```
  Consommation = (5 × Produit) + (2 × Réparation) + (3 × Transfert)
  ```

### **13.3 Analyse Financière**

**Projections à 3 Ans (Détail Financier)** :  

| Indicateur | Année 1 | Année 2 | Année 3 |
|------------|---------|---------|---------|
| Nombre de partenaires | 5,000 | 15,000 | 35,000 |
| CA estimé (F CFA) | 1.5 milliards | 5 milliards | 12 milliards |
| Coût moyen par crédit (F CFA) | 70 | 65 | 65 |
| Prix moyen par crédit (F CFA) | 90 | 85 | 85 |
| Marge brute | 22.2% | 23.5% | 23.5% |
| Marge nette après impôts | 15% | 18% | 18% |
| Investissement initial | 500 millions | - | - |
| ROI cumulé | 100% | 200% | 400% |

**Hypothèses de Projection** :  

- **Croissance des Partenaires** :  
  a. Année 1 : 5,000 partenaires (5 pays)  
  b. Année 2 : 15,000 partenaires (15 pays)  
  c. Année 3 : 35,000 partenaires (25 pays)  
  d. Taux de croissance mensuel : 15%  
  e. Taux de rétention à 12 mois : 75%  

- **Consommation de Crédits** :  
  a. Consommation moyenne/mois/partenaire : 1,500 crédits  
  b. Croissance de la consommation : 5% trimestriel  
  c. Taux de conversion : 25%  
  d. Panier moyen : 15,000 F CFA  

- **Coûts Opérationnels** :  
  a. Coûts fixes (infrastructure, équipe) : 300 millions F CFA/an  
  b. Coûts variables (support, marketing) : 25% du CA  
  c. Coûts de transaction : 5% du CA  
  d. Coûts de conformité : 3% du CA  

- **Hypothèses Macroéconomiques** :  
  a. Inflation annuelle : 2%  
  b. Taux de change stable  
  c. Croissance du marché digital : 25% annuel  
  d. Réglementations stables  

**Analyse de Sensibilité** :  

- **Scénario Optimiste (Taux de Croissance +20%)** :  
  a. CA Année 3 : 15 milliards F CFA  
  b. ROI cumulé : 550%  
  c. Durée de rentabilité : 18 mois  

- **Scénario Pessimiste (Taux de Croissance -10%)** :  
  a. CA Année 3 : 8 milliards F CFA  
  b. ROI cumulé : 200%  
  c. Durée de rentabilité : 30 mois  

- **Points Critiques** :  
  a. Taux de rétention des partenaires < 65%  
  b. Coûts d'acquisition client > 25,000 F CFA
